const { Client } = require("pg");
const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");

// Load environment variables from .env
dotenv.config();

// Also load PostgreSQL connection details from .env.psql if they're not in .env
if (!process.env.PGHOST) {
  dotenv.config({ path: path.join(__dirname, "..", ".env.psql") });
}

console.log("Using PostgreSQL connection details:");
console.log(`Host: ${process.env.PGHOST}`);
console.log(`Database: ${process.env.PGDATABASE}`);
console.log(`User: ${process.env.PGUSER}`);

// Create a PostgreSQL client
const client = new Client({
  host: process.env.PGHOST,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
  database: process.env.PGDATABASE,
  port: process.env.PGPORT,
  ssl: { rejectUnauthorized: false }, // Required for Supabase connections
});

// Function to execute SQL files in order
async function applyMigrations() {
  try {
    console.log("Connecting to database...");
    await client.connect();
    console.log("Connected successfully.");

    // Get all migration files
    const migrationsDir = path.join(__dirname, "..", "supabase", "migrations");
    const migrationFiles = fs
      .readdirSync(migrationsDir)
      .filter((file) => file.endsWith(".sql"))
      .sort(); // Sort to ensure correct order

    console.log(`Found ${migrationFiles.length} migration files.`);

    // Execute each migration file
    for (const file of migrationFiles) {
      console.log(`Applying migration: ${file}`);
      const filePath = path.join(migrationsDir, file);
      const sql = fs.readFileSync(filePath, "utf8");

      try {
        await client.query("BEGIN");
        await client.query(sql);
        await client.query("COMMIT");
        console.log(`Successfully applied migration: ${file}`);
      } catch (error) {
        await client.query("ROLLBACK");
        console.error(`Error applying migration ${file}: ${error.message}`);
        throw error; // Re-throw to stop the process
      }
    }

    console.log("All migrations applied successfully.");
  } catch (error) {
    console.error(`Database migration failed: ${error.message}`);
  } finally {
    // Close the database connection
    await client.end();
    console.log("Database connection closed.");
  }
}

// Run the migrations
applyMigrations()
  .then(() => {
    console.log("Migration process completed.");
    process.exit(0);
  })
  .catch((error) => {
    console.error(`Error in migration process: ${error.message}`);
    process.exit(1);
  });
