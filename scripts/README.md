# Stock Price Updater

This set of scripts manages a centralized stock price system using Supabase as the database and Yahoo Finance as the data source.

## Features

- Maintains a database of the top 900 most actively traded symbols
- Includes stocks, ETFs, indices, futures, forex, and bonds
- Bat<PERSON> fetches prices from Yahoo Finance (30 symbols at a time)
- Updates prices on a scheduled basis
- Provides functions to get prices for specific symbols

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file based on `.env.example`:
   ```
   cp .env.example .env
   ```

3. Update the `.env` file with your Supabase credentials.

4. Run the database migrations in the `/supabase/migrations` folder to create the necessary tables and functions.

## Usage

### Initialize Stock Symbols

To populate the database with the top 900 symbols:

```
npm run init
```

This will:
- Collect symbols from various sources (S&P 500, NASDAQ 100, etc.)
- Fetch basic information for each symbol
- Insert the symbols into the `stock_symbols` table

### Update Stock Prices

To update prices for symbols that need updating:

```
npm run update
```

This will:
- Get symbols that need updating (either new symbols or oldest prices)
- Fetch current price data from Yahoo Finance
- Update the `stock_prices` table

### Continuous Updates

To continuously update prices in batches:

```
npm run update-continuous
```

This will:
- Run the update process in a loop
- Process 30 symbols at a time
- Wait 2 seconds between batches
- Continue until all symbols have been updated

## Integration with Frontend

To display stock prices in your application:

1. Create an API endpoint that calls the `get_prices_for_symbols` function
2. Pass in the symbols from the user's positions
3. Return the cached prices to the frontend

Example API call:

```javascript
const getPositionPrices = async (symbols) => {
  const { data, error } = await supabase.rpc('get_prices_for_symbols', {
    p_symbols: symbols
  });
  
  if (error) throw error;
  return data;
};
```

## Maintenance

- The system is designed to prioritize updating symbols that haven't been updated recently
- You can adjust the batch size and delay between batches based on your needs
- Monitor the Yahoo Finance API usage to ensure you stay within rate limits
