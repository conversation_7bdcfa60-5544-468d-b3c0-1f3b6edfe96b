const { createClient } = require("@supabase/supabase-js");
const yahooFinance = require("yahoo-finance2").default;
const dotenv = require("dotenv");
const fs = require("fs");
const path = require("path");
const { updateStockSymbols } = require("./update-stock-prices");

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
// For server-side scripts like this one, we should use the service key
// as it has full admin access and bypasses Row Level Security
const supabaseKey = process.env.SUPABASE_ANON_KEY; // Using the anon key that you provided
const supabase = createClient(supabaseUrl, supabaseKey);

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log(`API Key available: ${supabaseKey ? "Yes" : "No"}`);

// Print the first 10 characters of the key for debugging
if (supabaseKey) {
  console.log(`API Key (first 10 chars): ${supabaseKey.substring(0, 10)}...`);
}

// Utility to sleep for a specified number of milliseconds
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Lists of symbols to include
const majorIndices = [
  "^GSPC", // S&P 500
  "^DJI", // Dow Jones Industrial Average
  "^IXIC", // NASDAQ Composite
  "^RUT", // Russell 2000
  "^VIX", // CBOE Volatility Index
  "^FTSE", // FTSE 100
  "^N225", // Nikkei 225
  "^HSI", // Hang Seng Index
  "^GDAXI", // DAX
  "^FCHI", // CAC 40
];

const popularETFs = [
  "SPY", // SPDR S&P 500 ETF
  "QQQ", // Invesco QQQ Trust
  "IWM", // iShares Russell 2000 ETF
  "VTI", // Vanguard Total Stock Market ETF
  "VOO", // Vanguard S&P 500 ETF
  "ARKK", // ARK Innovation ETF
  "XLF", // Financial Select Sector SPDR Fund
  "XLE", // Energy Select Sector SPDR Fund
  "GLD", // SPDR Gold Shares
  "SLV", // iShares Silver Trust
  "USO", // United States Oil Fund
  "TLT", // iShares 20+ Year Treasury Bond ETF
  "LQD", // iShares iBoxx $ Investment Grade Corporate Bond ETF
  "HYG", // iShares iBoxx $ High Yield Corporate Bond ETF
  "VXX", // iPath Series B S&P 500 VIX Short-Term Futures ETN
];

const popularFutures = [
  "ES=F", // E-mini S&P 500 Future
  "NQ=F", // E-mini NASDAQ 100 Future
  "YM=F", // Mini Dow Jones Industrial Average Future
  "RTY=F", // E-mini Russell 2000 Future
  "CL=F", // Crude Oil Future
  "GC=F", // Gold Future
  "SI=F", // Silver Future
  "HG=F", // Copper Future
  "ZC=F", // Corn Future
  "ZW=F", // Wheat Future
];

const popularForex = [
  "EURUSD=X", // EUR/USD
  "GBPUSD=X", // GBP/USD
  "USDJPY=X", // USD/JPY
  "AUDUSD=X", // AUD/USD
  "USDCAD=X", // USD/CAD
  "USDCHF=X", // USD/CHF
  "NZDUSD=X", // NZD/USD
  "EURJPY=X", // EUR/JPY
  "GBPJPY=X", // GBP/JPY
  "EURGBP=X", // EUR/GBP
];

const popularCrypto = [
  "BTC-USD", // Bitcoin
  "ETH-USD", // Ethereum
  "XRP-USD", // XRP
  "BCH-USD", // Bitcoin Cash
  "LTC-USD", // Litecoin
  "ADA-USD", // Cardano
  "DOT-USD", // Polkadot
  "LINK-USD", // Chainlink
  "XLM-USD", // Stellar
  "DOGE-USD", // Dogecoin
];

// Top 600 optionable stocks with high liquidity and options volume
const topOptionableStocks = [
  // Mega-cap tech
  "AAPL",
  "MSFT",
  "GOOGL",
  "GOOG",
  "AMZN",
  "META",
  "TSLA",
  "NVDA",
  "AVGO",
  "ORCL",
  "CSCO",
  "ADBE",
  "CRM",
  "INTC",
  "AMD",
  "IBM",
  "QCOM",
  "TXN",
  "AMAT",
  "MU",
  "NFLX",
  "PYPL",
  "BABA",
  "SHOP",
  "SNOW",
  "UBER",
  "ABNB",
  "DASH",
  "COIN",
  "PLTR",

  // Financial sector
  "JPM",
  "BAC",
  "WFC",
  "C",
  "GS",
  "MS",
  "AXP",
  "V",
  "MA",
  "SCHW",
  "BLK",
  "PNC",
  "TFC",
  "USB",
  "COF",
  "AIG",
  "MET",
  "PRU",
  "ALL",
  "TRV",

  // Healthcare
  "JNJ",
  "PFE",
  "MRK",
  "ABBV",
  "LLY",
  "BMY",
  "AMGN",
  "GILD",
  "BIIB",
  "REGN",
  "VRTX",
  "MRNA",
  "BNTX",
  "NVAX",
  "UNH",
  "CVS",
  "CI",
  "HUM",
  "ANTM",
  "MDT",

  // Consumer
  "WMT",
  "PG",
  "KO",
  "PEP",
  "COST",
  "HD",
  "LOW",
  "MCD",
  "SBUX",
  "NKE",
  "DIS",
  "CMCSA",
  "NFLX",
  "T",
  "VZ",
  "TMUS",
  "CHTR",
  "DISH",
  "AMZN",
  "EBAY",

  // Energy
  "XOM",
  "CVX",
  "COP",
  "EOG",
  "PXD",
  "OXY",
  "MPC",
  "PSX",
  "VLO",
  "SLB",
  "HAL",
  "BKR",
  "DVN",
  "MRO",
  "APA",
  "HES",
  "FANG",
  "KMI",
  "WMB",
  "ET",

  // Industrial
  "GE",
  "HON",
  "MMM",
  "CAT",
  "DE",
  "BA",
  "LMT",
  "RTX",
  "NOC",
  "GD",
  "UPS",
  "FDX",
  "CSX",
  "UNP",
  "NSC",
  "DAL",
  "UAL",
  "AAL",
  "LUV",
  "JBLU",

  // Materials
  "DD",
  "DOW",
  "LIN",
  "APD",
  "ECL",
  "SHW",
  "FCX",
  "NEM",
  "GOLD",
  "NUE",
  "X",
  "CLF",
  "AA",
  "MOS",
  "CF",
  "ALB",
  "STLD",
  "WPM",
  "MP",
  "LAC",

  // Utilities & Real Estate
  "NEE",
  "DUK",
  "SO",
  "D",
  "AEP",
  "XEL",
  "PEG",
  "ED",
  "EXC",
  "SRE",
  "AMT",
  "PLD",
  "CCI",
  "EQIX",
  "DLR",
  "O",
  "SPG",
  "WELL",
  "PSA",
  "AVB",

  // Consumer Discretionary
  "AMZN",
  "TSLA",
  "HD",
  "MCD",
  "NKE",
  "SBUX",
  "LOW",
  "TGT",
  "TJX",
  "BKNG",
  "EXPE",
  "MAR",
  "HLT",
  "CCL",
  "RCL",
  "NCLH",
  "GM",
  "F",
  "LCID",
  "RIVN",

  // Meme & Retail Favorites
  "GME",
  "AMC",
  "BB",
  "NOK",
  "BBBY",
  "KOSS",
  "EXPR",
  "DKNG",
  "PENN",
  "HOOD",
  "RBLX",
  "SOFI",
  "CLOV",
  "WISH",
  "TLRY",
  "SNDL",
  "ACB",
  "CGC",
  "SPCE",
  "PLUG",

  // Additional Tech
  "ZM",
  "PTON",
  "ROKU",
  "SQ",
  "TWLO",
  "NET",
  "CRWD",
  "OKTA",
  "DDOG",
  "FSLY",
  "APPS",
  "TTD",
  "SNAP",
  "PINS",
  "TWTR",
  "SPOT",
  "U",
  "RBLX",
  "MVIS",
  "FUBO",

  // Biotech & Pharma
  "MRNA",
  "BNTX",
  "NVAX",
  "PFE",
  "JNJ",
  "GILD",
  "REGN",
  "VRTX",
  "BIIB",
  "AMGN",
  "CRSP",
  "EDIT",
  "NTLA",
  "SRPT",
  "SGEN",
  "BMRN",
  "IONS",
  "ALNY",
  "EXAS",
  "ILMN",

  // Clean Energy & EV
  "TSLA",
  "RIVN",
  "LCID",
  "NIO",
  "XPEV",
  "LI",
  "FSR",
  "GOEV",
  "NKLA",
  "BLNK",
  "CHPT",
  "EVGO",
  "PLUG",
  "FCEL",
  "BE",
  "ENPH",
  "SEDG",
  "RUN",
  "STEM",
  "NOVA",

  // Semiconductors
  "NVDA",
  "AMD",
  "INTC",
  "TSM",
  "AVGO",
  "QCOM",
  "TXN",
  "MU",
  "AMAT",
  "LRCX",
  "KLAC",
  "ADI",
  "MRVL",
  "MCHP",
  "ON",
  "SWKS",
  "NXPI",
  "MPWR",
  "SMCI",
  "WOLF",

  // Software
  "MSFT",
  "ORCL",
  "ADBE",
  "CRM",
  "NOW",
  "INTU",
  "WDAY",
  "TEAM",
  "ZS",
  "PANW",
  "CRWD",
  "OKTA",
  "DDOG",
  "NET",
  "SNOW",
  "PLTR",
  "CFLT",
  "PATH",
  "AI",
  "GTLB",

  // Additional Financial
  "BRK-B",
  "BX",
  "KKR",
  "APO",
  "CG",
  "BAM",
  "TROW",
  "BEN",
  "IVZ",
  "NTRS",
  "STT",
  "DFS",
  "SYF",
  "ALLY",
  "LC",
  "SOFI",
  "UPST",
  "AFRM",
  "NU",
  "HOOD",

  // Additional Healthcare
  "ABT",
  "TMO",
  "DHR",
  "ISRG",
  "EW",
  "BSX",
  "ZBH",
  "SYK",
  "BDX",
  "BAX",
  "HOLX",
  "XRAY",
  "ALGN",
  "DXCM",
  "PODD",
  "TDOC",
  "DOCS",
  "ONEM",
  "CLOV",
  "OSCR",

  // Additional Consumer
  "ABNB",
  "DASH",
  "UBER",
  "LYFT",
  "GRUB",
  "CHWY",
  "CVNA",
  "ETSY",
  "W",
  "RH",
  "WSM",
  "BBWI",
  "LB",
  "GPS",
  "ANF",
  "AEO",
  "URBN",
  "ROST",
  "TJX",
  "M",

  // Additional Energy & Materials
  "LNG",
  "TELL",
  "FANG",
  "AR",
  "RRC",
  "EQT",
  "CHK",
  "SWN",
  "CTRA",
  "CNX",
  "CLF",
  "X",
  "NUE",
  "STLD",
  "RS",
  "FCX",
  "SCCO",
  "AA",
  "CENX",
  "MP",

  // Additional Industrial
  "PCAR",
  "ODFL",
  "JBHT",
  "XPO",
  "KNX",
  "CHRW",
  "EXPD",
  "FAST",
  "GWW",
  "POOL",
  "URI",
  "WAB",
  "TT",
  "IR",
  "DOV",
  "ITW",
  "ETN",
  "EMR",
  "ROK",
  "PH",

  // Additional Utilities & Real Estate
  "PCG",
  "EIX",
  "FE",
  "PPL",
  "NI",
  "CNP",
  "AES",
  "OGE",
  "LNT",
  "WEC",
  "VTR",
  "PEAK",
  "HTA",
  "DOC",
  "MPW",
  "SBRA",
  "OHI",
  "VNO",
  "BXP",
  "KRC",

  // Additional Consumer Discretionary
  "DKNG",
  "PENN",
  "MGM",
  "WYNN",
  "LVS",
  "CZR",
  "BYD",
  "BALY",
  "GENI",
  "FUBO",
  "CHDN",
  "DKNG",
  "RSI",
  "SKLZ",
  "ZNGA",
  "EA",
  "ATVI",
  "TTWO",
  "U",
  "RBLX",

  // Additional Retail Favorites
  "MARA",
  "RIOT",
  "BITF",
  "HUT",
  "BTBT",
  "CLSK",
  "SI",
  "COIN",
  "MSTR",
  "GBTC",
  "ARKK",
  "ARKW",
  "ARKF",
  "ARKG",
  "ARKX",
  "SARK",
  "TQQQ",
  "SQQQ",
  "SPXU",
  "SPXL",
];

// Function to get S&P 500 components
async function getSP500Components() {
  try {
    console.log("Fetching S&P 500 components...");
    // This is a simplified approach - in a real implementation, you might want to
    // scrape this data from a reliable source or use a paid API

    // For now, we'll use a static list of the largest S&P 500 companies
    const topSP500 = [
      "AAPL",
      "MSFT",
      "AMZN",
      "GOOGL",
      "GOOG",
      "META",
      "TSLA",
      "NVDA",
      "BRK-B",
      "JPM",
      "JNJ",
      "V",
      "PG",
      "UNH",
      "HD",
      "MA",
      "BAC",
      "DIS",
      "ADBE",
      "CRM",
      "CMCSA",
      "XOM",
      "VZ",
      "NFLX",
      "INTC",
      "PFE",
      "ABT",
      "KO",
      "PEP",
      "T",
      "CSCO",
      "WMT",
      "MRK",
      "CVX",
      "NKE",
      "TMO",
      "ABBV",
      "AVGO",
      "ACN",
      "PYPL",
      "MCD",
      "DHR",
      "NEE",
      "LLY",
      "COST",
      "MDT",
      "UNP",
      "BMY",
      "PM",
      "HON",
      "TXN",
      "QCOM",
      "LIN",
      "ORCL",
      "AMT",
      "IBM",
      "SBUX",
      "MMM",
      "C",
      "BA",
      "RTX",
      "GS",
      "LOW",
      "INTU",
      "BLK",
      "CHTR",
      "GILD",
      "MDLZ",
      "ISRG",
      "SPGI",
      "MO",
      "TGT",
      "BKNG",
      "AXP",
      "PLD",
      "AMGN",
      "CVS",
      "CI",
      "ANTM",
      "COP",
      "CAT",
      "TJX",
      "ZTS",
      "USB",
      "FIS",
      "TMUS",
      "MS",
      "SCHW",
      "CME",
      "BDX",
      "CCI",
      "PNC",
      "CB",
      "DUK",
      "SO",
      "ICE",
      "D",
      "EQIX",
      "SYK",
      "VRTX",
    ];

    return topSP500;
  } catch (error) {
    console.error(`Error fetching S&P 500 components: ${error.message}`);
    return [];
  }
}

// Function to get NASDAQ 100 components
async function getNasdaq100Components() {
  try {
    console.log("Fetching NASDAQ 100 components...");
    // This is a simplified approach - in a real implementation, you might want to
    // scrape this data from a reliable source or use a paid API

    // For now, we'll use a static list of the largest NASDAQ 100 companies
    const topNasdaq100 = [
      "AAPL",
      "MSFT",
      "AMZN",
      "GOOGL",
      "GOOG",
      "META",
      "TSLA",
      "NVDA",
      "PYPL",
      "ADBE",
      "NFLX",
      "CMCSA",
      "INTC",
      "CSCO",
      "PEP",
      "AVGO",
      "COST",
      "QCOM",
      "TXN",
      "TMUS",
      "CHTR",
      "SBUX",
      "INTU",
      "MDLZ",
      "ISRG",
      "BKNG",
      "GILD",
      "AMD",
      "AMGN",
      "VRTX",
      "REGN",
      "ILMN",
      "ATVI",
      "ADI",
      "FISV",
      "CSX",
      "BIIB",
      "AMAT",
      "LRCX",
      "ADSK",
      "MU",
      "ADP",
      "MNST",
      "ALGN",
      "CDNS",
      "WDAY",
      "ORLY",
      "CTAS",
      "KLAC",
      "SNPS",
      "ASML",
      "MELI",
      "NXPI",
      "PCAR",
      "PAYX",
      "FAST",
      "CTSH",
      "XLNX",
      "LULU",
      "EBAY",
      "VRSK",
      "DLTR",
      "CPRT",
      "ANSS",
      "SWKS",
      "MCHP",
      "SIRI",
      "VRSN",
      "IDXX",
      "CERN",
      "ALXN",
      "EXPE",
      "BMRN",
      "NTAP",
      "ULTA",
      "INCY",
      "CDW",
      "TTWO",
      "FOXA",
      "FOX",
      "ROST",
      "MXIM",
      "WDC",
      "UAL",
      "CTXS",
      "JBHT",
      "HSIC",
      "XRAY",
      "WLTW",
      "AAL",
    ];

    return topNasdaq100;
  } catch (error) {
    console.error(`Error fetching NASDAQ 100 components: ${error.message}`);
    return [];
  }
}

// Function to get most active stocks
async function getMostActiveStocks() {
  try {
    console.log("Fetching most active stocks...");
    // In a real implementation, you would fetch this from Yahoo Finance or another source
    // For now, we'll use a static list of commonly active stocks
    const mostActive = [
      "AAPL",
      "MSFT",
      "AMZN",
      "TSLA",
      "AMD",
      "NVDA",
      "META",
      "GOOGL",
      "NFLX",
      "BABA",
      "NIO",
      "PLTR",
      "GME",
      "AMC",
      "BB",
      "NOK",
      "SPCE",
      "PLUG",
      "SNAP",
      "TWTR",
      "UBER",
      "LYFT",
      "COIN",
      "RBLX",
      "HOOD",
      "RIVN",
      "LCID",
      "SOFI",
      "WISH",
      "CLOV",
      "TLRY",
      "SNDL",
      "ACB",
      "CGC",
      "DKNG",
      "PENN",
      "ABNB",
      "DASH",
      "ZM",
      "PTON",
      "MRNA",
      "PFE",
      "JNJ",
      "BNTX",
      "NVAX",
      "CRSP",
      "EDIT",
      "NTLA",
      "CRWD",
      "NET",
      "FSLY",
      "OKTA",
      "SNOW",
      "DDOG",
      "TWLO",
      "SHOP",
      "SQ",
      "PYPL",
      "AFRM",
      "U",
      "RBLX",
      "MVIS",
      "FUBO",
      "SKLZ",
      "OPEN",
      "UPST",
      "ASTR",
      "SPIR",
      "IONQ",
      "QS",
      "CHPT",
      "BLNK",
      "EVGO",
      "FCEL",
      "BE",
      "ENPH",
      "SEDG",
      "RUN",
      "STEM",
      "MP",
      "LAC",
      "ALB",
      "LTHM",
      "UUUU",
      "CCJ",
      "DNN",
      "UEC",
      "NXE",
      "TELL",
      "LNG",
    ];

    return mostActive;
  } catch (error) {
    console.error(`Error fetching most active stocks: ${error.message}`);
    return [];
  }
}

// Function to get popular bonds
async function getPopularBonds() {
  try {
    console.log("Adding popular bonds...");
    // Treasury bonds and popular bond ETFs
    const bonds = [
      "TLT", // iShares 20+ Year Treasury Bond ETF
      "IEF", // iShares 7-10 Year Treasury Bond ETF
      "SHY", // iShares 1-3 Year Treasury Bond ETF
      "BND", // Vanguard Total Bond Market ETF
      "AGG", // iShares Core U.S. Aggregate Bond ETF
      "LQD", // iShares iBoxx $ Investment Grade Corporate Bond ETF
      "HYG", // iShares iBoxx $ High Yield Corporate Bond ETF
      "JNK", // SPDR Bloomberg High Yield Bond ETF
      "MUB", // iShares National Muni Bond ETF
      "VCIT", // Vanguard Intermediate-Term Corporate Bond ETF
      "VCSH", // Vanguard Short-Term Corporate Bond ETF
      "BNDX", // Vanguard Total International Bond ETF
      "EMB", // iShares J.P. Morgan USD Emerging Markets Bond ETF
      "IGIB", // iShares Intermediate-Term Corporate Bond ETF
      "IGSB", // iShares Short-Term Corporate Bond ETF
      "GOVT", // iShares U.S. Treasury Bond ETF
      "USIG", // iShares Broad USD Investment Grade Corporate Bond ETF
      "SPTL", // SPDR Portfolio Long Term Treasury ETF
      "SPIB", // SPDR Portfolio Intermediate Term Corporate Bond ETF
      "SPSB", // SPDR Portfolio Short Term Corporate Bond ETF
    ];

    return bonds;
  } catch (error) {
    console.error(`Error adding popular bonds: ${error.message}`);
    return [];
  }
}

// Function to initialize the database with top symbols
async function initializeTopSymbols(maxSymbols = 600) {
  try {
    console.log(
      `Initializing database with up to ${maxSymbols} top symbols...`
    );

    // Collect symbols from various sources
    const sp500 = await getSP500Components();
    const nasdaq100 = await getNasdaq100Components();
    const mostActive = await getMostActiveStocks();
    const bonds = await getPopularBonds();

    // Combine all symbols and remove duplicates
    let allSymbols = [
      ...majorIndices,
      ...popularETFs,
      ...popularFutures,
      ...popularForex,
      ...popularCrypto,
      ...topOptionableStocks, // Add our hardcoded list of top optionable stocks
      ...sp500,
      ...nasdaq100,
      ...mostActive,
      ...bonds,
    ];

    // Remove duplicates
    allSymbols = [...new Set(allSymbols)];

    // Limit to maxSymbols
    allSymbols = allSymbols.slice(0, maxSymbols);

    console.log(`Collected ${allSymbols.length} unique symbols.`);

    // Process symbols in batches
    const batchSize = 30;
    const delayBetweenBatches = 1000; // 2 seconds

    for (let i = 0; i < allSymbols.length; i += batchSize) {
      const batch = allSymbols.slice(i, i + batchSize);
      console.log(
        `Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(
          allSymbols.length / batchSize
        )}...`
      );

      try {
        // Fetch basic info for each symbol - don't specify fields to avoid validation errors
        const symbolData = await yahooFinance.quote(batch);
        const results = Array.isArray(symbolData) ? symbolData : [symbolData];

        // Prepare data for database
        const stockData = results.map((quote, index) => {
          // Determine symbol type
          let type = "stock"; // Default
          const symbol = quote.symbol || batch[index];

          if (majorIndices.includes(symbol)) type = "index";
          else if (popularETFs.includes(symbol)) type = "etf";
          else if (popularFutures.includes(symbol)) type = "future";
          else if (popularForex.includes(symbol)) type = "forex";
          else if (popularCrypto.includes(symbol)) type = "crypto";
          else if (bonds.includes(symbol)) type = "bond";

          return {
            symbol,
            name: quote.shortName || quote.longName || symbol,
            type,
            exchange: quote.fullExchangeName || "Unknown",
            popularity_rank: i + index + 1, // Rank based on position in the list
          };
        });

        // Insert symbols into database
        for (const data of stockData) {
          await supabase.rpc("upsert_stock_symbol", {
            p_symbol: data.symbol,
            p_name: data.name,
            p_type: data.type,
            p_exchange: data.exchange,
            p_popularity_rank: data.popularity_rank,
          });
        }

        console.log(`Inserted ${stockData.length} symbols into database.`);

        // Sleep before next batch
        if (i + batchSize < allSymbols.length) {
          console.log(
            `Sleeping for ${delayBetweenBatches}ms before next batch...`
          );
          await sleep(delayBetweenBatches);
        }
      } catch (error) {
        console.error(`Error processing batch: ${error.message}`);
        // Continue with next batch
      }
    }

    console.log("Symbol initialization complete.");
    return allSymbols.length;
  } catch (error) {
    console.error(`Error initializing symbols: ${error.message}`);
    return 0;
  }
}

// If this script is run directly (not imported)
if (require.main === module) {
  // Parse command line arguments
  const args = process.argv.slice(2);
  const maxSymbols = parseInt(args[0]) || 900;

  // Initialize the database with top symbols
  initializeTopSymbols(maxSymbols)
    .then((count) => {
      console.log(`Added ${count} symbols to the database.`);
      process.exit(0);
    })
    .catch((error) => {
      console.error(`Error in main process: ${error.message}`);
      process.exit(1);
    });
}

// Export functions for use in other scripts
module.exports = {
  initializeTopSymbols,
  getSP500Components,
  getNasdaq100Components,
  getMostActiveStocks,
  getPopularBonds,
};
