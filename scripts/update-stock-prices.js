const { createClient } = require("@supabase/supabase-js");
const yahooFinance = require("yahoo-finance2").default;
const dotenv = require("dotenv");
const fs = require("fs");
const path = require("path");

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
// For server-side scripts like this one, we should use the service key
// as it has full admin access and bypasses Row Level Security
const supabaseKey = process.env.SUPABASE_ANON_KEY; // Using the anon key that you provided
const supabase = createClient(supabaseUrl, supabaseKey);

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log(`API Key available: ${supabaseKey ? "Yes" : "No"}`);

// Print the first 10 characters of the key for debugging
if (supabaseKey) {
  console.log(`API Key (first 10 chars): ${supabaseKey.substring(0, 10)}...`);
}

// Utility to sleep for a specified number of milliseconds
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Function to fetch stock data from Yahoo Finance
async function fetchStockData(symbols) {
  try {
    console.log(
      `Fetching data for ${symbols.length} symbols: ${symbols.join(", ")}`
    );
    // Don't specify fields to avoid validation errors
    const result = await yahooFinance.quote(symbols);

    // Handle both single result and array of results
    const results = Array.isArray(result) ? result : [result];
    return results.map((quote) => ({
      symbol: quote.symbol,
      name: quote.shortName || null,
      exchange: quote.fullExchangeName || null,
      current_price: quote.regularMarketPrice || null,
      previous_close: quote.regularMarketPreviousClose || null,
      open_price: quote.regularMarketOpen || null,
      day_high: quote.regularMarketDayHigh || null,
      day_low: quote.regularMarketDayLow || null,
      change_amount: quote.regularMarketChange || null,
      change_percent: quote.regularMarketChangePercent || null,
      volume: quote.regularMarketVolume || null,
      market_cap: quote.marketCap || null,
      fifty_two_week_high: quote.fiftyTwoWeekHigh || null,
      fifty_two_week_low: quote.fiftyTwoWeekLow || null,
      pe_ratio: quote.trailingPE || null,
      dividend_yield: quote.trailingAnnualDividendYield || null,
    }));
  } catch (error) {
    console.error(`Error fetching stock data: ${error.message}`);
    // Return empty array if there's an error
    return [];
  }
}

// Function to update stock symbols in the database
async function updateStockSymbols(stockData) {
  for (const data of stockData) {
    if (!data.symbol) continue;

    try {
      // Determine the type based on available data
      // This is a simple heuristic and might need refinement
      let type = "stock"; // Default
      if (data.symbol.endsWith("^")) type = "index";
      else if (data.symbol.includes("=F")) type = "future";
      else if (data.symbol.includes("-")) type = "bond";

      // Update the stock symbol
      await supabase.rpc("upsert_stock_symbol", {
        p_symbol: data.symbol,
        p_name: data.name || data.symbol,
        p_type: type,
        p_exchange: data.exchange || "Unknown",
        p_popularity_rank: 999, // Default rank, will be updated later
      });
    } catch (error) {
      console.error(`Error updating symbol ${data.symbol}: ${error.message}`);
    }
  }
}

// Function to update stock prices in the database
async function updateStockPrices(stockData) {
  for (const data of stockData) {
    if (!data.symbol || data.current_price === null) continue;

    try {
      // Update the stock price
      await supabase.rpc("upsert_stock_price", {
        p_symbol: data.symbol,
        p_current_price: data.current_price,
        p_previous_close: data.previous_close,
        p_open_price: data.open_price,
        p_day_high: data.day_high,
        p_day_low: data.day_low,
        p_change_amount: data.change_amount,
        p_change_percent: data.change_percent,
        p_volume: data.volume,
        p_market_cap: data.market_cap,
        p_fifty_two_week_high: data.fifty_two_week_high,
        p_fifty_two_week_low: data.fifty_two_week_low,
        p_pe_ratio: data.pe_ratio,
        p_dividend_yield: data.dividend_yield,
      });
    } catch (error) {
      console.error(
        `Error updating price for ${data.symbol}: ${error.message}`
      );
    }
  }
}

// Function to get symbols that need updating
async function getSymbolsForUpdate(limit = 50) {
  try {
    // First, try to get symbols that don't have any price data yet
    let { data: symbols, error } = await supabase.rpc(
      "get_symbols_without_prices",
      {
        p_limit: limit,
      }
    );

    if (error) throw error;

    // If we have symbols without prices, return those
    if (symbols && symbols.length > 0) {
      return symbols.map((s) => s.symbol);
    }

    // Otherwise, get symbols that need updating (oldest first)
    ({ data: symbols, error } = await supabase.rpc("get_symbols_for_update", {
      p_limit: limit,
    }));

    if (error) throw error;

    return symbols.map((s) => s.symbol);
  } catch (error) {
    console.error(`Error getting symbols for update: ${error.message}`);
    return [];
  }
}

// Main function to update stock data
async function updateStockData(batchSize = 50, delayBetweenBatches = 2000) {
  try {
    // Get symbols that need updating
    const symbols = await getSymbolsForUpdate(batchSize);

    if (symbols.length === 0) {
      console.log("No symbols need updating at this time.");
      return;
    }

    console.log(`Updating ${symbols.length} symbols...`);

    // Fetch data from Yahoo Finance
    const stockData = await fetchStockData(symbols);

    if (stockData.length === 0) {
      console.log("No data returned from Yahoo Finance.");
      return;
    }

    // Update symbols and prices in the database
    await updateStockSymbols(stockData);
    await updateStockPrices(stockData);

    console.log(`Successfully updated ${stockData.length} symbols.`);

    // Return the number of symbols updated
    return stockData.length;
  } catch (error) {
    console.error(`Error in updateStockData: ${error.message}`);
    return 0;
  }
}

// Function to run continuous updates
async function runContinuousUpdates(
  batchSize = 50,
  delayBetweenBatches = 2000,
  maxBatches = Infinity
) {
  let batchCount = 0;

  console.log(
    `Starting continuous updates with batch size ${batchSize} and delay ${delayBetweenBatches}ms`
  );

  while (batchCount < maxBatches) {
    const updatedCount = await updateStockData(batchSize, delayBetweenBatches);
    batchCount++;

    console.log(
      `Completed batch ${batchCount}. Updated ${updatedCount} symbols.`
    );

    // If we didn't update any symbols, we might be done
    if (updatedCount === 0 && batchCount > 1) {
      console.log("No more symbols to update. Exiting.");
      break;
    }

    // Sleep before the next batch
    await sleep(delayBetweenBatches);
  }

  console.log(`Completed ${batchCount} batches.`);
}

// If this script is run directly (not imported)
if (require.main === module) {
  // Parse command line arguments
  const args = process.argv.slice(2);
  const batchSize = parseInt(args[0]) || 50;
  const delayBetweenBatches = parseInt(args[1]) || 2000;
  const maxBatches = parseInt(args[2]) || Infinity;

  // Run the continuous updates
  runContinuousUpdates(batchSize, delayBetweenBatches, maxBatches)
    .then(() => {
      console.log("Stock data update process completed.");
      process.exit(0);
    })
    .catch((error) => {
      console.error(`Error in main process: ${error.message}`);
      process.exit(1);
    });
}

// Export functions for use in other scripts
module.exports = {
  fetchStockData,
  updateStockSymbols,
  updateStockPrices,
  getSymbolsForUpdate,
  updateStockData,
  runContinuousUpdates,
};
