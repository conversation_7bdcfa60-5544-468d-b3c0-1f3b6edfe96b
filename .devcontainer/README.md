# Five Rivers Options App DevContainer

This directory contains the configuration for the VSCode DevContainer setup for the Five Rivers Options web application.

## Requirements

To use this development setup, you need:

1. Docker Desktop or Docker Engine installed
2. Visual Studio Code
3. VSCode Remote - Containers extension installed

## Getting Started

1. Open the project in VSCode
2. When prompted, click "Reopen in Container" or run the command "Remote-Containers: Reopen in Container"
3. Wait for the container to build and initialize
4. Once inside the container, run one of these commands to initialize your project:
   - For a new Next.js project: `npx create-next-app@latest . --typescript --tailwind --eslint --app --import-alias "@/*"`
   - Or to install dependencies for an existing project: `npm install`

## Container Features

- Node.js 20 environment
- Pre-installed npm
- Running as root user for maximum flexibility
- Preconfigured VSCode with extensions for JavaScript/TypeScript development

## Ports

- Next.js app: [http://localhost:3000](http://localhost:3000)

## Commands

Common commands to use in the container:

- Create a new Next.js app: `npx create-next-app@latest`
- Install dependencies: `npm install`
- Start the development server: `npm run dev`
- Build the application: `npm run build`

## Notes

To add Shadcn UI to your project:
```bash
npm install shadcn-ui@latest
npx shadcn-ui@latest init
```

When you're ready to add Supabase:
```bash
npm install @supabase/auth-helpers-nextjs @supabase/supabase-js
npm install -D supabase
```

If you need GitHub features later, you can add them to your devcontainer.json:
```json
"features": {
  "ghcr.io/devcontainers/features/git:1": {},
  "ghcr.io/devcontainers/features/github-cli:1": {}
}
``` 