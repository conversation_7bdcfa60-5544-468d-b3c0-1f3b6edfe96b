FROM mcr.microsoft.com/devcontainers/javascript-node:0-20

# Install dependencies for development
RUN apt-get update && export DEBIAN_FRONTEND=noninteractive \
    && apt-get -y install git bash-completion \
    && apt-get clean -y && rm -rf /var/lib/apt/lists/*

# Install global packages for development
RUN npm install -g npm@9.6.6 \
    && npm install -g next@latest

# Set working directory
WORKDIR /workspaces/five-rivers-options

# Keep running as root (no USER directive) 
