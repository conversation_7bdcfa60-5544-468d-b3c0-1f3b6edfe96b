{"name": "Five Rivers Options App", "build": {"dockerfile": "Dockerfile"}, "forwardPorts": [3000], "postCreateCommand": "cd frontend && npm install", "customizations": {"vscode": {"extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next", "dsznajder.es7-react-js-snippets", "yoavbls.pretty-ts-errors", "aaron-bond.better-comments", "formulahendry.auto-rename-tag", "steoates.autoimport", "streetsidesoftware.code-spell-checker", "naumovs.color-highlight"], "settings": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.tabSize": 2, "typescript.preferences.importModuleSpecifier": "non-relative", "javascript.preferences.importModuleSpecifier": "non-relative", "typescript.suggest.autoImports": true, "javascript.suggest.autoImports": true, "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "tailwindCSS.emmetCompletions": true}}}}