-- Create stock_prices table
CREATE TABLE IF NOT EXISTS stock_prices (
  id BIGSERIAL PRIMARY KEY,
  symbol TEXT REFERENCES stock_symbols(symbol) ON DELETE CASCADE,
  current_price NUMERIC(18, 4),
  previous_close NUMERIC(18, 4),
  open_price NUMERIC(18, 4),
  day_high NUMERIC(18, 4),
  day_low NUMERIC(18, 4),
  change_amount NUMERIC(18, 4),
  change_percent NUMERIC(10, 4),
  volume BIGINT,
  market_cap BIGINT,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Additional fields that might be useful
  fifty_two_week_high NUMERIC(18, 4),
  fifty_two_week_low NUMERIC(18, 4),
  pe_ratio NUMERIC(14, 4),
  dividend_yield NUMERIC(10, 4)
);

-- Add indexes for faster queries
CREATE UNIQUE INDEX IF NOT EXISTS idx_stock_prices_symbol ON stock_prices(symbol);
CREATE INDEX IF NOT EXISTS idx_stock_prices_last_updated ON stock_prices(last_updated);

-- Add a comment to the table
COMMENT ON TABLE stock_prices IS 'Table storing the latest price data for financial instruments';

-- Create a function to update the last_updated timestamp
CREATE OR REPLACE FUNCTION update_stock_prices_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_updated = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the last_updated timestamp
CREATE TRIGGER set_stock_prices_updated_at
BEFORE UPDATE ON stock_prices
FOR EACH ROW
EXECUTE FUNCTION update_stock_prices_updated_at();
