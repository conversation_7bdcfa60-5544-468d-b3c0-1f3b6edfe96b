-- Create stock_symbols table
CREATE TABLE IF NOT EXISTS stock_symbols (
  symbol TEXT PRIMARY KEY,
  name TEXT,
  type TEXT, -- stock, etf, index, future, bond, etc.
  exchange TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  popularity_rank INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_stock_symbols_type ON stock_symbols(type);
CREATE INDEX IF NOT EXISTS idx_stock_symbols_popularity ON stock_symbols(popularity_rank);
CREATE INDEX IF NOT EXISTS idx_stock_symbols_is_active ON stock_symbols(is_active);

-- Add a comment to the table
COMMENT ON TABLE stock_symbols IS 'Table storing information about stock symbols and other financial instruments';
