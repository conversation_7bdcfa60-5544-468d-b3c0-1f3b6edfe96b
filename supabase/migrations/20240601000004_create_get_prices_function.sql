-- Function to get prices for specific symbols
CREATE OR REPLACE FUNCTION get_prices_for_symbols(p_symbols TEXT[])
RETURNS TABLE (
  symbol TEXT,
  current_price NUMERIC,
  previous_close NUMERIC,
  change_amount NUMERIC,
  change_percent NUMERIC,
  last_updated TIMES<PERSON>MP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sp.symbol,
    sp.current_price,
    sp.previous_close,
    sp.change_amount,
    sp.change_percent,
    sp.last_updated
  FROM stock_prices sp
  WHERE sp.symbol = ANY(p_symbols);
END;
$$ LANGUAGE plpgsql;

-- Function to get symbols that don't have price data yet
CREATE OR REPLACE FUNCTION get_symbols_without_prices(p_limit INTEGER DEFAULT 30)
RETURNS TABLE (symbol TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT ss.symbol
  FROM stock_symbols ss
  LEFT JOIN stock_prices sp ON ss.symbol = sp.symbol
  WHERE sp.symbol IS NULL
  AND ss.is_active = TRUE
  ORDER BY ss.popularity_rank ASC NULLS LAST
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;
