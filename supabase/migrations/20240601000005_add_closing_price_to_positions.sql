-- Add closing_price column to positions table if it doesn't exist
-- This column stores the price at which a position was closed for analytics

DO $$ 
BEGIN
    -- Check if the column exists, if not add it
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'positions' 
        AND column_name = 'closing_price'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.positions 
        ADD COLUMN closing_price NUMERIC;
        
        -- Add a comment to explain the column
        COMMENT ON COLUMN public.positions.closing_price IS 'Price at which the position was closed, used for profit/loss calculations and analytics';
    END IF;
END $$;

-- Create an index on closing_price for analytics queries
CREATE INDEX IF NOT EXISTS positions_closing_price_idx ON public.positions(closing_price) 
WHERE closing_price IS NOT NULL;

-- Add a comment to the table about the new column
COMMENT ON TABLE public.positions IS 'Table storing options trading positions with opening and closing price data for analytics';
