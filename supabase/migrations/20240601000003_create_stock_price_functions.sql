-- Function to upsert stock price data
CREATE OR REPLACE FUNCTION upsert_stock_price(
  p_symbol TEXT,
  p_current_price NUMERIC,
  p_previous_close NUMERIC,
  p_open_price NUMERIC,
  p_day_high NUMERIC,
  p_day_low NUMERIC,
  p_change_amount NUMERIC,
  p_change_percent NUMERIC,
  p_volume BIGINT,
  p_market_cap BIGINT,
  p_fifty_two_week_high NUMERIC DEFAULT NULL,
  p_fifty_two_week_low NUMERIC DEFAULT NULL,
  p_pe_ratio NUMERIC DEFAULT NULL,
  p_dividend_yield NUMERIC DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
  INSERT INTO stock_prices (
    symbol, 
    current_price, 
    previous_close, 
    open_price, 
    day_high, 
    day_low, 
    change_amount, 
    change_percent, 
    volume, 
    market_cap,
    fifty_two_week_high,
    fifty_two_week_low,
    pe_ratio,
    dividend_yield,
    last_updated
  )
  VALUES (
    p_symbol, 
    p_current_price, 
    p_previous_close, 
    p_open_price, 
    p_day_high, 
    p_day_low, 
    p_change_amount, 
    p_change_percent, 
    p_volume, 
    p_market_cap,
    p_fifty_two_week_high,
    p_fifty_two_week_low,
    p_pe_ratio,
    p_dividend_yield,
    NOW()
  )
  ON CONFLICT (symbol) 
  DO UPDATE SET 
    current_price = p_current_price,
    previous_close = p_previous_close,
    open_price = p_open_price,
    day_high = p_day_high,
    day_low = p_day_low,
    change_amount = p_change_amount,
    change_percent = p_change_percent,
    volume = p_volume,
    market_cap = p_market_cap,
    fifty_two_week_high = COALESCE(p_fifty_two_week_high, stock_prices.fifty_two_week_high),
    fifty_two_week_low = COALESCE(p_fifty_two_week_low, stock_prices.fifty_two_week_low),
    pe_ratio = COALESCE(p_pe_ratio, stock_prices.pe_ratio),
    dividend_yield = COALESCE(p_dividend_yield, stock_prices.dividend_yield),
    last_updated = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to get symbols that need updating (oldest first)
CREATE OR REPLACE FUNCTION get_symbols_for_update(p_limit INTEGER DEFAULT 30) 
RETURNS TABLE (symbol TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT sp.symbol
  FROM stock_prices sp
  JOIN stock_symbols ss ON sp.symbol = ss.symbol
  WHERE ss.is_active = TRUE
  ORDER BY sp.last_updated ASC NULLS FIRST
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;
