-- Function to upsert a stock symbol
CREATE OR REPLACE FUNCTION upsert_stock_symbol(
  p_symbol TEXT,
  p_name TEXT,
  p_type TEXT,
  p_exchange TEXT,
  p_popularity_rank INTEGER
) RETURNS VOID AS $$
BEGIN
  INSERT INTO stock_symbols (symbol, name, type, exchange, popularity_rank, updated_at)
  VALUES (p_symbol, p_name, p_type, p_exchange, p_popularity_rank, NOW())
  ON CONFLICT (symbol) 
  DO UPDATE SET 
    name = p_name,
    type = p_type,
    exchange = p_exchange,
    popularity_rank = p_popularity_rank,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to mark symbols as active or inactive
CREATE OR REPLACE FUNCTION update_symbol_active_status(
  p_symbol TEXT,
  p_is_active BOOLEAN
) RETURNS VOID AS $$
BEGIN
  UPDATE stock_symbols
  SET 
    is_active = p_is_active,
    updated_at = NOW()
  WHERE symbol = p_symbol;
END;
$$ LANGUAGE plpgsql;
