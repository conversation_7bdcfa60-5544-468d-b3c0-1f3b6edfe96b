# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
frontend/node_modules
.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
coverage

# next.js
.next/
out/

# production
build
dist

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
# .env
# .env*.local
.env.development
.env.test
.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json

# Supabase


# Logs
logs
*.log

# System Files
.DS_Store
Thumbs.db 