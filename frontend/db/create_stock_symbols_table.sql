-- Create stock_symbols table for symbol normalization
CREATE TABLE IF NOT EXISTS stock_symbols (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  input_variant TEXT NOT NULL,
  normalized_symbol TEXT NOT NULL,
  display_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS stock_symbols_input_variant_idx ON stock_symbols(input_variant);
CREATE INDEX IF NOT EXISTS stock_symbols_normalized_symbol_idx ON stock_symbols(normalized_symbol);

-- Add some common stock symbols to start with
INSERT INTO stock_symbols (input_variant, normalized_symbol, display_name)
VALUES 
  ('AAPL', 'AAPL', 'Apple Inc.'),
  ('Apple', 'AAPL', 'Apple Inc.'),
  ('MSFT', 'MSFT', 'Microsoft Corporation'),
  ('Microsoft', 'MSFT', 'Microsoft Corporation'),
  ('GOOGL', 'GOOGL', 'Alphabet Inc.'),
  ('Google', 'GOOGL', 'Alphabet Inc.'),
  ('AMZN', 'AMZN', 'Amazon.com, Inc.'),
  ('Amazon', 'AMZN', 'Amazon.com, Inc.'),
  ('TSLA', 'TSLA', 'Tesla, Inc.'),
  ('Tesla', 'TSLA', 'Tesla, Inc.'),
  ('META', 'META', 'Meta Platforms, Inc.'),
  ('Facebook', 'META', 'Meta Platforms, Inc.'),
  ('FB', 'META', 'Meta Platforms, Inc.'),
  ('NFLX', 'NFLX', 'Netflix, Inc.'),
  ('Netflix', 'NFLX', 'Netflix, Inc.')
ON CONFLICT (input_variant) DO NOTHING;
