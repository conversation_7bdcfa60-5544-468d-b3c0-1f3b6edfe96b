import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { createServerClient } from "@supabase/ssr";

// List of routes that require authentication
const protectedRoutes = [
  "/dashboard",
  "/dashboard/positions",
  "/dashboard/positions/new",
  "/dashboard/settings",
];

// List of auth routes
const authRoutes = ["/login", "/register", "/reset-password"];

export async function middleware(request: NextRequest) {
  try {
    // Create a response object that we'll modify and return
    const response = NextResponse.next();

    // Create the Supabase client with the updated @supabase/ssr pattern
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get: (name) => {
            return request.cookies.get(name)?.value;
          },
          set: (name, value, options) => {
            request.cookies.set({
              name,
              value,
              ...options,
            });
            response.cookies.set({
              name,
              value,
              ...options,
            });
          },
          remove: (name, options) => {
            request.cookies.set({
              name,
              value: "",
              ...options,
            });
            response.cookies.set({
              name,
              value: "",
              ...options,
            });
          },
        },
      }
    );

    // Get the path of the current request
    const path = request.nextUrl.pathname;

    // Get the session from supabase - await this call
    const {
      data: { session },
    } = await supabase.auth.getSession();

    // Check if the user is authenticated
    const isAuthenticated = !!session;

    // Check if the route requires authentication
    const isProtectedRoute = protectedRoutes.some((route) =>
      path.startsWith(route)
    );

    // Check if the route is an auth route
    const isAuthRoute = authRoutes.some((route) => path.startsWith(route));

    // If the route requires authentication and the user is not authenticated,
    // redirect to the login page
    if (isProtectedRoute && !isAuthenticated) {
      const redirectUrl = new URL("/login", request.url);
      // Add the return_to query parameter so the user can be redirected back
      // after login
      redirectUrl.searchParams.set("return_to", path);
      return NextResponse.redirect(redirectUrl);
    }

    // If the user is authenticated and trying to access an auth route,
    // redirect to the dashboard
    if (isAuthRoute && isAuthenticated) {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }

    return response;
  } catch (error) {
    console.error("Middleware error:", error);

    // In case of error, allow the request to continue
    // This prevents middleware errors from blocking the application
    return NextResponse.next();
  }
}

// Match all requests
export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
};
