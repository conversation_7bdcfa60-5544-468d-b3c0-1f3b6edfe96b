import { createBrowserClient as createSupabaseBrowserClient } from "@supabase/ssr";
import { createClient as createSupabaseClient } from "@supabase/supabase-js";
import { type Session, type User } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";

// Type for the auth state context
export type AuthState = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
};

// For client components
export const createBrowserClient = () => {
  return createSupabaseBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
};

// Singleton instance for client components
let supabaseClientInstance: ReturnType<
  typeof createSupabaseBrowserClient<Database>
> | null = null;

/**
 * Create a Supabase client for use in the browser (singleton pattern)
 */
export function createClientSupabaseClient() {
  if (!supabaseClientInstance) {
    // Initialize the client if it doesn't exist
    supabaseClientInstance = createSupabaseBrowserClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
  }

  return supabaseClientInstance;
}

// For server components and server actions
export const getServerClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string;

  return createSupabaseClient(supabaseUrl, supabaseKey);
};
