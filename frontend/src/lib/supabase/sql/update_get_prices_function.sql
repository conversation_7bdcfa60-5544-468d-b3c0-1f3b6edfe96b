-- Update the get_prices_for_symbols function to include the name
CREATE OR REPLACE FUNCTION get_prices_for_symbols(p_symbols text[])
RETURNS TABLE (
  symbol text,
  current_price numeric,
  previous_close numeric,
  change_amount numeric,
  change_percent numeric,
  last_updated timestamp with time zone,
  name text -- Add name to the returned columns
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sp.symbol,
    sp.current_price,
    sp.previous_close,
    sp.change_amount,
    sp.change_percent,
    sp.last_updated,
    ss.name -- Include name from stock_symbols
  FROM 
    stock_prices sp
  JOIN
    stock_symbols ss ON sp.symbol = ss.symbol
  WHERE 
    sp.symbol = ANY(p_symbols);
END;
$$ LANGUAGE plpgsql;
