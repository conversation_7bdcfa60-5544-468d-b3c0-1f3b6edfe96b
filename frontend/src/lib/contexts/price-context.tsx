"use client";

import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useEffect,
  useCallback,
} from "react";
import { fetchPrices } from "@/lib/services/price-service";
import { getMarketAwareUpdater } from "@/lib/services/market-aware-updater";

// Type definitions
interface PriceData {
  price: number;
  previousClose: number;
  change: number;
  changePercent: number;
  lastUpdated: string;
  name?: string; // Optional name of the underlying security
}

interface SymbolRegistration {
  symbol: string;
  positionStatus?: "open" | "closed";
  isVisible?: boolean;
  lastSeen: Date;
}

interface PriceContextType {
  priceData: Record<string, PriceData | null>;
  registerSymbol: (
    symbol: string,
    positionStatus?: "open" | "closed",
    isVisible?: boolean
  ) => void;
  unregisterSymbol: (symbol: string) => void;
  clearInvalidSymbols: () => void;
  refreshSymbol: (symbol: string) => Promise<void>;
  refreshAllSymbols: () => Promise<void>;
  forceRefreshClosedPositions: () => Promise<void>;
  isLoading: Record<string, boolean>;
  isRefreshing: Record<string, boolean>;
}

// Create the context with a default value
const PriceContext = createContext<PriceContextType>({
  priceData: {},
  registerSymbol: () => {},
  unregisterSymbol: () => {},
  clearInvalidSymbols: () => {},
  refreshSymbol: async () => {},
  refreshAllSymbols: async () => {},
  forceRefreshClosedPositions: async () => {},
  isLoading: {},
  isRefreshing: {},
});

// Custom hook to use the price context
export const usePriceContext = () => useContext(PriceContext);

interface PriceContextProviderProps {
  children: React.ReactNode;
  refreshInterval?: number; // in milliseconds (optional override)
}

export function PriceContextProvider({
  children,
  refreshInterval, // No default - will be calculated dynamically
}: PriceContextProviderProps) {
  // State to store price data for all symbols
  const [priceData, setPriceData] = useState<Record<string, PriceData | null>>(
    {}
  );

  // State to track which symbols are currently being loaded
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

  // State to track which symbols are being refreshed (but already have data)
  const [isRefreshing, setIsRefreshing] = useState<Record<string, boolean>>({});

  // State to track user activity
  const [isUserActive, setIsUserActive] = useState<boolean>(true);
  const [isPageVisible, setIsPageVisible] = useState<boolean>(true);

  // Dynamic refresh interval based on market status (for monitoring)
  const [currentRefreshInterval, setCurrentRefreshInterval] =
    useState<number>(60000); // Default 1 minute

  // Session ID for global cache (disabled)
  // const sessionIdRef = useRef<string>(`session-${Date.now()}-${Math.random()}`);

  // Set to track pending symbols that need to be fetched
  const [pendingSymbols, setPendingSymbols] = useState<Set<string>>(new Set());

  // Ref to track the timeout for batched requests
  const pendingRequestRef = useRef<NodeJS.Timeout | null>(null);

  // Ref to track all registered symbols with metadata for smart refresh
  const registeredSymbolsRef = useRef<Map<string, SymbolRegistration>>(
    new Map()
  );

  // Helper function to check if market is open using market-aware updater
  const isMarketOpen = (): boolean => {
    try {
      const marketUpdater = getMarketAwareUpdater();
      return marketUpdater.getMarketStatus().isOpen;
    } catch {
      // Fallback to simple UTC check if market updater fails
      const now = new Date();
      const utcHour = now.getUTCHours();
      const utcDay = now.getUTCDay();
      return utcDay >= 1 && utcDay <= 5 && utcHour >= 14 && utcHour < 21;
    }
  };

  // Calculate optimal refresh interval based on market status
  const calculateRefreshInterval = useCallback((): number => {
    // Use override if provided
    if (refreshInterval) return refreshInterval;

    try {
      const marketUpdater = getMarketAwareUpdater();
      const marketStatus = marketUpdater.getMarketStatus();

      if (marketStatus.isOpen) {
        return 30000; // 30 seconds during market hours (more aggressive)
      } else if (marketStatus.isWeekend || marketStatus.isHoliday) {
        return 300000; // 5 minutes on weekends/holidays (less aggressive)
      } else {
        return 60000; // 1 minute after hours on weekdays (more aggressive)
      }
    } catch (error) {
      console.warn(
        "Failed to get market status, using default interval:",
        error
      );
      return 60000; // Default to 1 minute
    }
  }, [refreshInterval]);

  // Calculate optimal staleness threshold based on market status
  const calculateStalenessThreshold = useCallback((): number => {
    try {
      const marketUpdater = getMarketAwareUpdater();
      const marketStatus = marketUpdater.getMarketStatus();

      if (marketStatus.isOpen) {
        return 30000; // 30 seconds during market hours
      } else if (marketStatus.isWeekend || marketStatus.isHoliday) {
        return 300000; // 5 minutes on weekends/holidays
      } else {
        return 120000; // 2 minutes after hours on weekdays
      }
    } catch (error) {
      console.warn(
        "Failed to get market status for staleness, using default:",
        error
      );
      return 30000; // Default to 30 seconds
    }
  }, []);

  // Helper function to validate a symbol
  const isValidSymbol = (symbol: string): boolean => {
    // Symbol should be at least 1 character
    // This allows single-letter symbols like "F" (Ford)
    return symbol.length >= 1;
  };

  // Function to register a symbol for price updates with position awareness
  const registerSymbol = useCallback(
    (
      symbol: string,
      positionStatus?: "open" | "closed",
      isVisible: boolean = true
    ) => {
      if (!symbol) return;

      // Validate the symbol before registering
      if (!isValidSymbol(symbol)) {
        console.log(`Skipping invalid symbol: ${symbol}`);
        return;
      }

      // Add to registered symbols with metadata for smart refresh
      registeredSymbolsRef.current.set(symbol, {
        symbol,
        positionStatus,
        isVisible,
        lastSeen: new Date(),
      });

      // Add to pending symbols if not already being tracked
      if (
        !priceData[symbol] &&
        !pendingSymbols.has(symbol) &&
        !isLoading[symbol]
      ) {
        setPendingSymbols((prev) => {
          const newSet = new Set(prev);
          newSet.add(symbol);
          return newSet;
        });

        // The useEffect will handle scheduling the batch request
      }
    },
    [priceData, pendingSymbols, isLoading]
  );

  // Function to unregister a symbol
  const unregisterSymbol = useCallback(
    (symbol: string) => {
      if (!symbol) return;

      // Remove from registered symbols
      registeredSymbolsRef.current.delete(symbol);

      // Global cache disabled - no cleanup needed

      // Remove from pending symbols if present
      if (pendingSymbols.has(symbol)) {
        setPendingSymbols((prev) => {
          const newSet = new Set(prev);
          newSet.delete(symbol);
          return newSet;
        });
      }

      // Note: We don't remove from priceData to keep the cache
    },
    [pendingSymbols]
  );

  // Function to clear invalid symbols
  const clearInvalidSymbols = useCallback(() => {
    const symbolEntries = [...registeredSymbolsRef.current.entries()];

    // Filter out invalid symbols
    symbolEntries.forEach(([symbol]) => {
      if (!isValidSymbol(symbol)) {
        console.log(`Removing invalid symbol: ${symbol}`);
        registeredSymbolsRef.current.delete(symbol);
      }
    });
  }, []);

  // Function to fetch all pending symbols in a single batch request
  const fetchPendingPrices = useCallback(async () => {
    pendingRequestRef.current = null;

    if (pendingSymbols.size === 0) return;

    const symbolsToFetch = [...pendingSymbols];

    // Clear pending symbols
    setPendingSymbols(new Set());

    // Mark symbols as loading or refreshing based on whether we have data
    symbolsToFetch.forEach((symbol) => {
      if (priceData[symbol]) {
        setIsRefreshing((prev) => ({ ...prev, [symbol]: true }));
      } else {
        setIsLoading((prev) => ({ ...prev, [symbol]: true }));
      }
    });

    try {
      // Single API call for all pending symbols
      const data = await fetchPrices(symbolsToFetch);

      // Global cache disabled - no update needed

      // Update price data
      setPriceData((prev) => {
        const newData = { ...prev };
        symbolsToFetch.forEach((symbol) => {
          newData[symbol] = data[symbol];
        });
        return newData;
      });
    } catch (error) {
      console.error("Failed to fetch prices:", error);
    } finally {
      // Mark symbols as no longer loading or refreshing
      symbolsToFetch.forEach((symbol) => {
        setIsLoading((prev) => ({ ...prev, [symbol]: false }));
        setIsRefreshing((prev) => ({ ...prev, [symbol]: false }));
      });
    }
  }, [pendingSymbols, priceData]);

  // Function to refresh a specific symbol
  const refreshSymbol = useCallback(
    async (symbol: string) => {
      if (!symbol || isLoading[symbol]) return;

      // If we already have data for this symbol, mark as refreshing instead of loading
      if (priceData[symbol]) {
        setIsRefreshing((prev) => ({ ...prev, [symbol]: true }));
      } else {
        setIsLoading((prev) => ({ ...prev, [symbol]: true }));
      }

      try {
        const data = await fetchPrices([symbol], true); // Force refresh
        setPriceData((prev) => ({ ...prev, [symbol]: data[symbol] }));
      } catch (error) {
        console.error(`Failed to refresh price for ${symbol}:`, error);
      } finally {
        setIsLoading((prev) => ({ ...prev, [symbol]: false }));
        setIsRefreshing((prev) => ({ ...prev, [symbol]: false }));
      }
    },
    [isLoading, priceData]
  );

  // Function to refresh all registered symbols with smart prioritization
  const refreshAllSymbols = useCallback(async () => {
    const symbolEntries = [...registeredSymbolsRef.current.entries()];
    if (symbolEntries.length === 0) return;

    // Filter and prioritize symbols based on position status and visibility
    const now = Date.now();
    const stalenessThreshold = calculateStalenessThreshold();

    // Separate symbols by priority
    const openPositionSymbols: string[] = [];
    const closedPositionSymbols: string[] = [];
    const otherSymbols: string[] = [];

    symbolEntries.forEach(([symbol, registration]) => {
      const cachedData = priceData[symbol];

      // Check if symbol needs refresh
      const needsRefresh =
        !cachedData ||
        !cachedData.lastUpdated ||
        now - new Date(cachedData.lastUpdated).getTime() > stalenessThreshold;

      if (!needsRefresh) return;

      // Prioritize based on position status and visibility
      if (registration.positionStatus === "open" && registration.isVisible) {
        openPositionSymbols.push(symbol);
      } else if (
        registration.positionStatus === "closed" &&
        registration.isVisible
      ) {
        closedPositionSymbols.push(symbol);
      } else {
        otherSymbols.push(symbol);
      }
    });

    // Combine symbols with priority order: open positions first, then closed, then others
    const symbolsToRefresh = [
      ...openPositionSymbols,
      ...closedPositionSymbols,
      ...otherSymbols,
    ];

    if (symbolsToRefresh.length === 0) {
      console.log("All symbols are recently updated, skipping refresh");
      return;
    }

    console.log(
      `Refreshing ${symbolsToRefresh.length} symbols (${openPositionSymbols.length} open, ${closedPositionSymbols.length} closed, ${otherSymbols.length} other)`
    );

    // Mark symbols as loading or refreshing based on whether we have data
    symbolsToRefresh.forEach((symbol) => {
      if (priceData[symbol]) {
        setIsRefreshing((prev) => ({ ...prev, [symbol]: true }));
      } else {
        setIsLoading((prev) => ({ ...prev, [symbol]: true }));
      }
    });

    try {
      // Optimized batch processing for global cache
      const batchSize = 225; // Increased batch size since we're using global cache
      for (let i = 0; i < symbolsToRefresh.length; i += batchSize) {
        const batch = symbolsToRefresh.slice(i, i + batchSize);
        const data = await fetchPrices(batch, true); // Force refresh

        setPriceData((prev) => {
          const newData = { ...prev };
          batch.forEach((symbol) => {
            newData[symbol] = data[symbol];
          });
          return newData;
        });

        // Reduced delay between batches since global cache is much faster
        if (i + batchSize < symbolsToRefresh.length) {
          await new Promise((resolve) => setTimeout(resolve, 500)); // Reduced from 2000ms to 500ms
        }
      }
    } catch (error) {
      console.error("Failed to refresh all prices:", error);
    } finally {
      // Mark all symbols as no longer loading or refreshing
      symbolsToRefresh.forEach((symbol) => {
        setIsLoading((prev) => ({ ...prev, [symbol]: false }));
        setIsRefreshing((prev) => ({ ...prev, [symbol]: false }));
      });
    }
  }, [priceData, calculateStalenessThreshold]);

  // Function to force refresh closed positions (when user switches to closed tab)
  const forceRefreshClosedPositions = useCallback(async () => {
    const symbolEntries = [...registeredSymbolsRef.current.entries()];

    // Get only closed position symbols
    const closedPositionSymbols = symbolEntries
      .filter(([, registration]) => registration.positionStatus === "closed")
      .map(([symbol]) => symbol);

    if (closedPositionSymbols.length === 0) {
      console.log("No closed position symbols to refresh");
      return;
    }

    console.log(
      `Force refreshing ${closedPositionSymbols.length} closed position symbols`
    );

    // Mark symbols as refreshing
    closedPositionSymbols.forEach((symbol) => {
      if (priceData[symbol]) {
        setIsRefreshing((prev) => ({ ...prev, [symbol]: true }));
      } else {
        setIsLoading((prev) => ({ ...prev, [symbol]: true }));
      }
    });

    try {
      // Force refresh all closed position symbols
      const data = await fetchPrices(closedPositionSymbols, true);

      setPriceData((prev) => {
        const newData = { ...prev };
        closedPositionSymbols.forEach((symbol) => {
          newData[symbol] = data[symbol];
        });
        return newData;
      });

      console.log(
        `Successfully refreshed ${closedPositionSymbols.length} closed position symbols`
      );
    } catch (error) {
      console.error("Failed to force refresh closed positions:", error);
    } finally {
      // Mark symbols as no longer loading/refreshing
      closedPositionSymbols.forEach((symbol) => {
        setIsLoading((prev) => ({ ...prev, [symbol]: false }));
        setIsRefreshing((prev) => ({ ...prev, [symbol]: false }));
      });
    }
  }, [priceData]);

  // Set up position-aware periodic refresh
  useEffect(() => {
    const effectiveInterval = calculateRefreshInterval();

    const intervalId = setInterval(() => {
      if (registeredSymbolsRef.current.size === 0) return;

      // Check if we have any open positions
      const hasOpenPositions = [...registeredSymbolsRef.current.values()].some(
        (registration) => registration.positionStatus === "open"
      );

      // Simplified refresh logic: Always refresh during market hours
      // Remove position-aware restrictions that were causing slow updates
      const shouldRefresh = true; // Always refresh when market is open/extended hours

      if (shouldRefresh) {
        // Check market conditions using extended hours
        try {
          const marketUpdater = getMarketAwareUpdater();

          // Use extended hours for open positions, regular market hours for closed positions
          if (hasOpenPositions) {
            // Open positions: Use extended hours (9:00 AM - 4:30 PM ET)
            if (marketUpdater.shouldUpdateExtended()) {
              refreshAllSymbols();
            }
          } else {
            // Closed positions only: Use regular market hours + weekdays
            const marketStatus = marketUpdater.getMarketStatus();
            if (
              marketStatus.isOpen ||
              (!marketStatus.isWeekend && !marketStatus.isHoliday)
            ) {
              refreshAllSymbols();
            }
          }
        } catch {
          // Fallback: refresh during extended hours (9:00 AM - 4:30 PM ET)
          const now = new Date();
          const utcHour = now.getUTCHours();
          const utcDay = now.getUTCDay();

          // Extended hours: 9:00 AM ET (14:00 UTC) to 4:30 PM ET (21:30 UTC)
          if (
            utcDay >= 1 &&
            utcDay <= 5 &&
            utcHour >= 14 &&
            (utcHour < 21 || (utcHour === 21 && now.getUTCMinutes() < 30))
          ) {
            refreshAllSymbols();
          }
        }
      }
    }, effectiveInterval);

    return () => clearInterval(intervalId);
  }, [
    calculateRefreshInterval,
    refreshAllSymbols,
    isPageVisible,
    isUserActive,
  ]);

  // Clean up pending request on unmount
  useEffect(() => {
    return () => {
      if (pendingRequestRef.current) {
        clearTimeout(pendingRequestRef.current);
      }
    };
  }, []);

  // Set up user activity detection
  useEffect(() => {
    // Track page visibility
    const handleVisibilityChange = () => {
      setIsPageVisible(!document.hidden);
    };

    // Track user interaction (mouse movement, clicks, keyboard)
    let activityTimer: NodeJS.Timeout;
    const resetActivityTimer = () => {
      setIsUserActive(true);
      clearTimeout(activityTimer);
      // Extended timeout for closed positions - 20 minutes instead of 5
      // Open positions ignore this timeout completely
      activityTimer = setTimeout(() => setIsUserActive(false), 20 * 60 * 1000);
    };

    // Set up event listeners
    document.addEventListener("visibilitychange", handleVisibilityChange);
    document.addEventListener("mousemove", resetActivityTimer);
    document.addEventListener("mousedown", resetActivityTimer);
    document.addEventListener("keypress", resetActivityTimer);
    document.addEventListener("scroll", resetActivityTimer);
    document.addEventListener("touchstart", resetActivityTimer);

    // Initialize activity timer
    resetActivityTimer();

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      document.removeEventListener("mousemove", resetActivityTimer);
      document.removeEventListener("mousedown", resetActivityTimer);
      document.removeEventListener("keypress", resetActivityTimer);
      document.removeEventListener("scroll", resetActivityTimer);
      document.removeEventListener("touchstart", resetActivityTimer);
      clearTimeout(activityTimer);
    };
  }, []);

  // Update refresh interval based on market status changes
  useEffect(() => {
    const updateInterval = () => {
      const newInterval = calculateRefreshInterval();
      setCurrentRefreshInterval(newInterval);

      try {
        const marketUpdater = getMarketAwareUpdater();
        const marketStatus = marketUpdater.getMarketStatus();
        console.log(
          `Price context refresh interval updated: ${newInterval}ms (Market: ${
            marketStatus.isOpen ? "Open" : "Closed"
          })`
        );
      } catch {
        console.log(`Price context refresh interval updated: ${newInterval}ms`);
      }
    };

    // Update immediately
    updateInterval();

    // Update every 5 minutes to catch market status changes
    const intervalId = setInterval(updateInterval, 5 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, [calculateRefreshInterval, setCurrentRefreshInterval]);

  // Global cache enabled - using optimized batch processing
  useEffect(() => {
    console.log("Price context initialized with global cache optimization");

    // Start client-side batch updater for consistent cache updates
    const startBatchUpdater = async () => {
      try {
        const { startClientBatchUpdater } = await import(
          "../services/client-batch-updater"
        );
        startClientBatchUpdater();
        console.log("Client batch updater started successfully");
      } catch (error) {
        console.error("Failed to start client batch updater:", error);
      }
    };

    startBatchUpdater();

    // Cleanup on unmount
    return () => {
      const stopBatchUpdater = async () => {
        try {
          const { stopClientBatchUpdater } = await import(
            "../services/client-batch-updater"
          );
          stopClientBatchUpdater();
        } catch (error) {
          console.error("Failed to stop client batch updater:", error);
        }
      };
      stopBatchUpdater();
    };
  }, []);

  // Clear invalid symbols on mount
  useEffect(() => {
    // Run once on mount to clean up any invalid symbols
    clearInvalidSymbols();

    // Set up an interval to periodically clean up invalid symbols
    const cleanupInterval = setInterval(clearInvalidSymbols, 604800000); // Every 7 days (weekly)

    return () => clearInterval(cleanupInterval);
  }, [clearInvalidSymbols]);

  // Effect to trigger fetch when pending symbols change
  useEffect(() => {
    if (pendingSymbols.size > 0 && !pendingRequestRef.current) {
      // Reduced delay for faster response with global cache
      pendingRequestRef.current = setTimeout(fetchPendingPrices, 200); // Reduced from 500ms to 200ms
    }
  }, [pendingSymbols, fetchPendingPrices]);

  // Value to provide to context consumers
  const value = {
    priceData,
    registerSymbol,
    unregisterSymbol,
    clearInvalidSymbols,
    refreshSymbol,
    refreshAllSymbols,
    forceRefreshClosedPositions,
    isLoading,
    isRefreshing,
  };

  return (
    <PriceContext.Provider value={value}>{children}</PriceContext.Provider>
  );
}
