import { z } from "zod";

/**
 * Base position schema with common validations
 */
const basePositionSchema = {
  symbol: z
    .string()
    .min(1, "Symbol is required")
    .max(10, "Symbol must be 10 characters or less"),
  strategy_type: z.string().min(1, "Strategy type is required"),
  open_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  expiry_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  strike_prices: z
    .array(z.number())
    .min(1, "At least one strike price is required"),
  credit_debit_amount: z.number(),
  stock_price_at_open: z.number().nullable(),
  vix_value: z.number().nullable(),
  num_contracts: z.number().int().positive().default(1),
  commission_paid: z.number().nullable(),
  notes: z.string().nullable(),
};

/**
 * Schema for creating a new position
 */
export const createPositionSchema = z.object({
  ...basePositionSchema,
  status: z.enum(["open", "closed"]).default("open"),
  close_date: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format")
    .nullable(),
  profit_loss: z.number().nullable(),
  tags: z.array(z.string()).optional(),
});

/**
 * Schema for updating an existing position
 */
export const updatePositionSchema = z.object({
  id: z.string().uuid("Invalid position ID"),
  ...z.object(basePositionSchema).partial().shape,
  status: z.enum(["open", "closed"]).optional(),
  close_date: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format")
    .nullable()
    .optional(),
  profit_loss: z.number().nullable().optional(),
  tags: z.array(z.string()).optional(),
});

/**
 * Schema for closing a position
 */
export const closePositionSchema = z.object({
  id: z.string().uuid("Invalid position ID"),
  close_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  closing_price: z.number().min(0, "Closing price must be a positive number"),
});

/**
 * Schema for filtering positions
 */
export const positionFilterSchema = z.object({
  status: z.enum(["open", "closed", "all"]).optional(),
  symbol: z.string().optional(),
  strategy_type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  dateRange: z
    .object({
      start: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format")
        .optional(),
      end: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format")
        .optional(),
    })
    .optional(),
});

/**
 * Schema for position tags
 */
export const positionTagSchema = z.object({
  tag_name: z
    .string()
    .min(1, "Tag name is required")
    .max(50, "Tag name must be 50 characters or less"),
});

/**
 * Type for creating a new position
 */
export type CreatePositionValues = z.infer<typeof createPositionSchema>;

/**
 * Type for updating a position
 */
export type UpdatePositionValues = z.infer<typeof updatePositionSchema>;

/**
 * Type for closing a position
 */
export type ClosePositionValues = z.infer<typeof closePositionSchema>;

/**
 * Type for position filters
 */
export type PositionFilterValues = z.infer<typeof positionFilterSchema>;
