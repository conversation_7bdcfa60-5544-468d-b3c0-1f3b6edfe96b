import { fromZonedTime, toZonedTime } from "date-fns-tz";

// US Market timezone (Eastern Time)
const MARKET_TIMEZONE = "America/New_York";

// US Market holidays for 2024 (can be expanded)
const US_MARKET_HOLIDAYS_2024 = [
  "2024-01-01", // New Year's Day
  "2024-01-15", // <PERSON> Jr. Day
  "2024-02-19", // Presidents' Day
  "2024-03-29", // Good Friday
  "2024-05-27", // Memorial Day
  "2024-06-19", // Juneteenth
  "2024-07-04", // Independence Day
  "2024-09-02", // Labor Day
  "2024-11-28", // Thanksgiving Day
  "2024-12-25", // Christmas Day
];

/**
 * Checks if the US stock market is currently open
 * Regular trading hours: 9:30 AM - 4:00 PM Eastern Time, Monday-Friday
 */
export function isMarketOpen(): boolean {
  // Get current date in Eastern Time
  const now = new Date();
  const etNow = toZonedTime(now, MARKET_TIMEZONE);

  // Get day of week (0 = Sunday, 6 = Saturday)
  const day = etNow.getDay();

  // Check if it's a weekend
  if (day === 0 || day === 6) {
    return false;
  }

  // Check if it's a holiday
  const dateString = etNow.toISOString().split("T")[0];
  if (US_MARKET_HOLIDAYS_2024.includes(dateString)) {
    return false;
  }

  // Check if within trading hours (9:30 AM - 4:00 PM ET)
  const hours = etNow.getHours();
  const minutes = etNow.getMinutes();

  // Before 9:30 AM
  if (hours < 9 || (hours === 9 && minutes < 30)) {
    return false;
  }

  // After 4:00 PM
  if (hours >= 16) {
    return false;
  }

  return true;
}

/**
 * Determines the appropriate cache TTL based on market status
 * - During market hours: 60 seconds (1 minute)
 * - After hours on weekdays: 300 seconds (5 minutes)
 * - Weekends/Holidays: 3600 seconds (1 hour)
 */
export function getCacheTTL(): number {
  // Get current date in Eastern Time
  const now = new Date();
  const etNow = toZonedTime(now, MARKET_TIMEZONE);

  // Get day of week (0 = Sunday, 6 = Saturday)
  const day = etNow.getDay();

  // Weekend
  if (day === 0 || day === 6) {
    return 3600; // 1 hour
  }

  // Check if it's a holiday
  const dateString = etNow.toISOString().split("T")[0];
  if (US_MARKET_HOLIDAYS_2024.includes(dateString)) {
    return 3600; // 1 hour
  }

  // Check if within trading hours (9:30 AM - 4:00 PM ET)
  const hours = etNow.getHours();
  const minutes = etNow.getMinutes();

  // During market hours
  if ((hours > 9 || (hours === 9 && minutes >= 30)) && hours < 16) {
    return 60; // 1 minute
  }

  // After hours on a weekday
  return 300; // 5 minutes
}

/**
 * Gets the next market open time
 * Returns a Date object representing the next time the market will open
 */
export function getNextMarketOpenTime(): Date {
  const now = new Date();
  const etNow = toZonedTime(now, MARKET_TIMEZONE);

  let nextOpenDate = new Date(etNow);

  // Set time to 9:30 AM
  nextOpenDate.setHours(9, 30, 0, 0);

  // If it's already past 9:30 AM, move to next day
  if (
    etNow.getHours() > 9 ||
    (etNow.getHours() === 9 && etNow.getMinutes() >= 30)
  ) {
    nextOpenDate.setDate(nextOpenDate.getDate() + 1);
  }

  // Keep advancing days until we find a valid trading day
  let daysChecked = 0;
  while (daysChecked < 10) {
    // Safety limit
    const day = nextOpenDate.getDay();
    const dateString = nextOpenDate.toISOString().split("T")[0];

    // If it's a weekday and not a holiday, we found the next trading day
    if (
      day !== 0 &&
      day !== 6 &&
      !US_MARKET_HOLIDAYS_2024.includes(dateString)
    ) {
      break;
    }

    // Move to next day
    nextOpenDate.setDate(nextOpenDate.getDate() + 1);
    daysChecked++;
  }

  // Convert back to UTC
  return fromZonedTime(nextOpenDate, MARKET_TIMEZONE);
}
