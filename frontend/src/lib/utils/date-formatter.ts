import { format, formatDistanceToNow } from 'date-fns';
import { parseISO } from 'date-fns';

/**
 * Format a date string consistently using UTC to avoid hydration errors
 * @param dateString ISO date string to format
 * @param formatStr Format string for date-fns
 * @returns Formatted date string
 */
export function formatDate(dateString: string, formatStr: string = 'MMM d, yyyy'): string {
  // Parse the ISO string
  const date = parseISO(dateString);
  
  // Create a UTC date by setting the time to noon UTC
  // This ensures consistent rendering across server and client
  const utcDate = new Date(Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    12, 0, 0
  ));
  
  return format(utcDate, formatStr);
}

/**
 * Format a date string as a relative time (e.g., "2 days ago")
 * @param dateString ISO date string
 * @param options Options for formatDistanceToNow
 * @returns Formatted relative time string
 */
export function formatRelativeTime(
  dateString: string, 
  options: { addSuffix?: boolean } = { addSuffix: true }
): string {
  // Parse the ISO string
  const date = parseISO(dateString);
  
  // Create a UTC date
  const utcDate = new Date(Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    date.getUTCHours(),
    date.getUTCMinutes(),
    date.getUTCSeconds()
  ));
  
  return formatDistanceToNow(utcDate, options);
}
