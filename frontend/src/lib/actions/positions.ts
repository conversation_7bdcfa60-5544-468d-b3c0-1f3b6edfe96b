"use server";
/* eslint-disable @typescript-eslint/no-explicit-any */

import { revalidatePath } from "next/cache";
import { createActionSupabaseClient } from "@/lib/supabase/server";
import type {
  Position,
  NewPosition,
  UpdatePosition,
  PositionFilters,
  PositionSort,
} from "@/types/position";
import {
  createPositionSchema,
  updatePositionSchema,
  closePositionSchema,
} from "@/lib/validations/position";

/**
 * Create a new position
 */
export async function createPosition(position: NewPosition) {
  try {
    // Validate the input data
    const validatedData = createPositionSchema.parse(position);

    // Get the authenticated user from Supabase
    const supabase = await createActionSupabaseClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Unauthorized");
    }

    // Extract position data without tags

    const { tags, ...positionData } = validatedData;

    // Insert the position into the database
    const { data, error } = await supabase
      .from("positions")
      .insert({
        ...positionData,
        user_id: user.id,
      } as any)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // If tags were provided, create tag-position relationships
    if (tags && tags.length > 0) {
      // Create tag entries for each tag
      const tagInserts = tags.map((tag) => ({
        position_id: data.id,
        tag_name: tag,
      }));

      const { error: tagError } = await supabase
        .from("position_tags")
        .insert(tagInserts);

      if (tagError) {
        console.error("Error creating tags:", tagError);
        // Continue without tags rather than failing completely
      }
    }

    // Revalidate the positions page
    revalidatePath("/dashboard/positions");

    return { success: true, data };
  } catch (error) {
    console.error("Error creating position:", error);
    return { success: false, error };
  }
}

/**
 * Get a single position by ID
 */
export async function getPosition(id: string): Promise<Position | null> {
  try {
    // Get the authenticated user from Supabase
    const supabase = await createActionSupabaseClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Unauthorized");
    }

    // Get the position
    const { data: position, error } = await supabase
      .from("positions")
      .select("*")
      .eq("id", id as any)
      .eq("user_id", user.id as any)
      .single();

    if (error) {
      throw error;
    }

    if (!position) {
      return null;
    }

    // Fetch tags for this position
    const { data: tags, error: tagsError } = await supabase
      .from("position_tags")
      .select("id, tag_name")
      .eq("position_id", position.id);

    if (tagsError) {
      console.error("Error fetching tags:", tagsError);
      // Continue without tags rather than failing completely
    }

    return {
      ...position,
      tags: tags || [],
    } as Position;
  } catch (error) {
    console.error("Error getting position:", error);
    return null;
  }
}

/**
 * Get positions with optional filtering and sorting
 */
export async function getPositions(
  filters?: PositionFilters,
  sort?: PositionSort,
  page: number = 1,
  pageSize: number = 10
): Promise<Position[] | null> {
  try {
    const supabase = await createActionSupabaseClient();

    // Get the current user
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error("User not authenticated");
    }

    // Start building the query
    let query = supabase
      .from("positions")
      .select("*", { count: "exact" })
      .eq("user_id", user.id as unknown as string);

    // Apply filters if provided
    if (filters) {
      if (filters.status && filters.status !== "all") {
        query = query.eq("status", filters.status as unknown as string);
      }

      if (filters.symbol) {
        query = query.ilike("symbol", `%${filters.symbol}%`);
      }

      if (filters.strategy_type) {
        query = query.eq(
          "strategy_type",
          filters.strategy_type as unknown as string
        );
      }

      if (filters.dateRange?.start) {
        query = query.gte("open_date", filters.dateRange.start);
      }

      if (filters.dateRange?.end) {
        query = query.lte("open_date", filters.dateRange.end);
      }
    }

    // Apply sorting if provided
    if (sort) {
      query = query.order(sort.field as unknown as string, {
        ascending: sort.direction === "asc",
      });
    } else {
      // Default sort by open_date desc
      query = query.order("open_date", { ascending: false });
    }

    // Apply pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    query = query.range(from, to);

    // Execute the query
    const { data: positions, error } = await query;

    if (error) {
      throw error;
    }

    // Fetch tags for all positions
    const positionIds = positions.map((p) => p.id);

    // If there are no positions, return empty array
    if (positionIds.length === 0) {
      return [] as Position[];
    }

    const { data: allTags, error: tagsError } = await supabase
      .from("position_tags")
      .select("position_id, id, tag_name")
      .in("position_id", positionIds);

    if (tagsError) {
      console.error("Error fetching tags:", tagsError);
      // Continue without tags rather than failing completely
    }

    // Return positions with their tags
    return positions.map((pos) => ({
      ...pos,
      tags: allTags
        ? allTags
            .filter((tag) => tag.position_id === pos.id)
            .map((tag) => ({ id: tag.id, tag_name: tag.tag_name }))
        : [],
    })) as Position[];
  } catch (error) {
    console.error("Error fetching positions:", error);
    return null;
  }
}

/**
 * Update a position
 */
export async function updatePosition(position: UpdatePosition) {
  try {
    // Validate the input data
    const validatedData = updatePositionSchema.parse(position);

    // Get the authenticated user from Supabase
    const supabase = await createActionSupabaseClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Unauthorized");
    }

    // Destructure the position ID and tags

    const { id, tags, ...updateData } = validatedData;

    // Update the position
    const { data: updatedPosition, error } = await supabase
      .from("positions")
      .update(updateData as any)
      .eq("id", id as any)
      .eq("user_id", user.id as any)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Handle tags if provided
    if (tags) {
      try {
        // First delete existing tags for this position
        const { error: deleteError } = await supabase
          .from("position_tags")
          .delete()
          .eq("position_id", id);

        if (deleteError) {
          console.error("Error deleting existing tags:", deleteError);
        }

        // Then insert new tags if there are any
        if (tags.length > 0) {
          const tagInserts = tags.map((tag) => ({
            position_id: id,
            tag_name: tag,
          }));

          const { error: insertError } = await supabase
            .from("position_tags")
            .insert(tagInserts);

          if (insertError) {
            console.error("Error inserting new tags:", insertError);
          }
        }
      } catch (tagError) {
        console.error("Error updating tags:", tagError);
        // Continue without tags rather than failing completely
      }
    }

    // Revalidate the positions page
    revalidatePath("/dashboard/positions");
    revalidatePath(`/dashboard/positions/${id}`);

    return { success: true, data: updatedPosition };
  } catch (error) {
    console.error("Error in updatePosition:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Close a position
 */
export async function closePosition(data: {
  id: string;
  close_date: string;
  closing_price: number;
}) {
  try {
    // Validate the input data
    const validatedData = closePositionSchema.parse(data);

    // Get the authenticated user from Supabase
    const supabase = await createActionSupabaseClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Unauthorized");
    }

    // Get the position to calculate profit/loss
    const { data: position, error: positionError } = await supabase
      .from("positions")
      .select("*")
      .eq("id", validatedData.id)
      .eq("user_id", user.id)
      .single();

    if (positionError) {
      throw positionError;
    }

    if (!position) {
      throw new Error("Position not found");
    }

    // Calculate profit/loss based on strategy type and credit/debit amount
    let profitLoss = 0;

    if (position.credit_debit_amount >= 0) {
      // For credit strategies (positive credit_debit_amount)
      // Profit = credit received - closing cost
      profitLoss = position.credit_debit_amount - validatedData.closing_price;
    } else {
      // For debit strategies (negative credit_debit_amount)
      // Profit = closing price - debit paid
      profitLoss =
        validatedData.closing_price - Math.abs(position.credit_debit_amount);
    }

    // Adjust for number of contracts
    if (position.num_contracts > 1) {
      profitLoss *= position.num_contracts;
    }

    // Update the position to closed status
    const { data: updatedPosition, error } = await supabase
      .from("positions")
      .update({
        status: "closed",
        close_date: validatedData.close_date,
        closing_price: validatedData.closing_price,
        profit_loss: profitLoss,
      })
      .eq("id", validatedData.id)
      .eq("user_id", user.id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Revalidate the positions page
    revalidatePath("/dashboard/positions");
    revalidatePath(`/dashboard/positions/${validatedData.id}`);

    return { success: true, data: updatedPosition };
  } catch (error) {
    console.error("Error closing position:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Delete a position
 */
export async function deletePosition(id: string) {
  try {
    // Get the authenticated user from Supabase
    const supabase = await createActionSupabaseClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Unauthorized");
    }

    // Delete the position
    const { error } = await supabase
      .from("positions")
      .delete()
      .eq("id", id as any)
      .eq("user_id", user.id as any);

    if (error) {
      throw error;
    }

    // Revalidate the positions page
    revalidatePath("/dashboard/positions");

    return { success: true };
  } catch (error) {
    console.error("Error deleting position:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Get all tags for the current user
 */
/**
 * Get all unique tags for the current user
 */
export async function getAllTags() {
  try {
    // Get the authenticated user from Supabase
    const supabase = await createActionSupabaseClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Unauthorized");
    }

    // Get all unique tags for the user's positions
    const { data, error } = await supabase
      .from("position_tags")
      .select(
        `
        id,
        tag_name,
        position_id,
        positions!inner(user_id)
      `
      )
      .eq("positions.user_id", user.id)
      .order("tag_name");

    if (error) {
      throw error;
    }

    // Extract unique tag names
    const uniqueTags = Array.from(
      new Set(data.map((item: any) => item.tag_name))
    ).map((tagName: string) => ({
      id: data.find((item: any) => item.tag_name === tagName)?.id || "",
      tag_name: tagName,
    }));

    return { success: true, data: uniqueTags || [] };
  } catch (error) {
    console.error("Error fetching tags:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      data: [],
    };
  }
}

/**
 * Get tags for a specific position
 */
export async function getTagsByPosition(positionId: string) {
  try {
    const supabase = await createActionSupabaseClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Unauthorized");
    }

    // Get tags for the specific position
    const { data, error } = await supabase
      .from("position_tags")
      .select("id, tag_name")
      .eq("position_id", positionId);

    if (error) {
      throw error;
    }

    return {
      success: true,
      data: data || [],
    };
  } catch (error) {
    console.error("Error fetching position tags:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      data: [],
    };
  }
}

/**
 * Update expired positions to closed status
 * This calls a database function that updates any open positions
 * with expiry dates in the past to closed status
 */
export async function updateExpiredPositions() {
  try {
    // Get the authenticated user from Supabase
    const supabase = await createActionSupabaseClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error("Unauthorized");
    }

    // Call the database function to update expired positions
    const { data, error } = await supabase.rpc("update_expired_positions");

    if (error) {
      throw error;
    }

    // We don't need to revalidate the path here since we're fetching fresh data
    // after calling this function in our page components

    return {
      success: true,
      updatedCount: data,
    };
  } catch (error) {
    console.error("Error updating expired positions:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      updatedCount: 0,
    };
  }
}
