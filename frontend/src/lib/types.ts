export type User = {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
  profile_data?: Record<string, unknown>;
};

export type Position = {
  id: string;
  user_id: string;
  symbol: string;
  strategy_type:
    | "Call"
    | "Put"
    | "Spread"
    | "Iron Condor"
    | "Butterfly"
    | "Other";
  open_date: string;
  close_date?: string | null;
  expiry_date: string;
  strike_prices: number[]; // For multiple legs
  credit_debit_amount: number;
  stock_price_at_open?: number | null;
  vix_value?: number | null;
  num_contracts?: number;
  commission_paid?: number | null;
  notes?: string | null;
  status: "open" | "closed";
  profit_loss?: number | null;
  created_at: string;
  updated_at: string;
};
