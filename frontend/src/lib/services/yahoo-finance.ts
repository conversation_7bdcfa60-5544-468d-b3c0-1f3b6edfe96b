import yahooFinance from 'yahoo-finance2';
import { getCachedPrice, cachePrice } from './price-cache';
import { normalizeSymbol } from './symbol-normalizer';
import { isMarketOpen, getCacheTTL } from '../utils/market-hours';

// Rate limiting - to avoid hitting Yahoo Finance API limits
const MIN_REQUEST_INTERVAL = 500; // milliseconds
let lastRequestTime = 0;

/**
 * Fetch the current stock price from Yahoo Finance
 * @param symbol The stock symbol to look up
 * @returns The current price or null if not found
 */
export async function fetchStockPrice(symbol: string): Promise<number | null> {
  try {
    // Normalize the symbol
    const normalizedSymbol = await normalizeSymbol(symbol);
    if (!normalizedSymbol) {
      return null;
    }
    
    // Check cache first
    const cachedPrice = getCachedPrice(normalizedSymbol);
    if (cachedPrice !== null) {
      return cachedPrice;
    }
    
    // Rate limiting
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
      await new Promise(resolve => setTimeout(resolve, MIN_REQUEST_INTERVAL - timeSinceLastRequest));
    }
    
    // Update last request time
    lastRequestTime = Date.now();
    
    // Fetch from Yahoo Finance
    const quote = await yahooFinance.quote(normalizedSymbol);
    
    // Extract the price
    const price = quote.regularMarketPrice;
    if (typeof price !== 'number' || isNaN(price)) {
      return null;
    }
    
    // Cache the result
    const ttl = getCacheTTL();
    cachePrice(normalizedSymbol, price, ttl);
    
    return price;
  } catch (error) {
    console.error(`Error fetching stock price for ${symbol}:`, error);
    return null;
  }
}

/**
 * Batch fetch stock prices for multiple symbols
 * @param symbols Array of stock symbols
 * @returns Object mapping symbols to prices
 */
export async function batchFetchStockPrices(symbols: string[]): Promise<Record<string, number | null>> {
  const result: Record<string, number | null> = {};
  
  // First check cache for all symbols
  for (const symbol of symbols) {
    const normalizedSymbol = await normalizeSymbol(symbol);
    const cachedPrice = getCachedPrice(normalizedSymbol);
    
    if (cachedPrice !== null) {
      result[symbol] = cachedPrice;
    } else {
      result[symbol] = null;
    }
  }
  
  // Get symbols that need to be fetched
  const symbolsToFetch = symbols.filter(symbol => result[symbol] === null);
  
  if (symbolsToFetch.length === 0) {
    return result;
  }
  
  try {
    // Rate limiting
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
      await new Promise(resolve => setTimeout(resolve, MIN_REQUEST_INTERVAL - timeSinceLastRequest));
    }
    
    // Update last request time
    lastRequestTime = Date.now();
    
    // Normalize symbols
    const normalizedSymbols = await Promise.all(symbolsToFetch.map(normalizeSymbol));
    
    // Fetch from Yahoo Finance
    const quotes = await yahooFinance.quote(normalizedSymbols);
    
    // Process results
    const ttl = getCacheTTL();
    const quotesArray = Array.isArray(quotes) ? quotes : [quotes];
    
    for (let i = 0; i < quotesArray.length; i++) {
      const quote = quotesArray[i];
      const originalSymbol = symbolsToFetch[i];
      const normalizedSymbol = normalizedSymbols[i];
      
      if (quote && typeof quote.regularMarketPrice === 'number' && !isNaN(quote.regularMarketPrice)) {
        const price = quote.regularMarketPrice;
        result[originalSymbol] = price;
        
        // Cache the result
        cachePrice(normalizedSymbol, price, ttl);
      }
    }
    
    return result;
  } catch (error) {
    console.error(`Error batch fetching stock prices:`, error);
    return result;
  }
}

/**
 * Get market status information
 * @returns Object with market status information
 */
export function getMarketStatus() {
  return {
    isOpen: isMarketOpen(),
    cacheTTL: getCacheTTL(),
  };
}
