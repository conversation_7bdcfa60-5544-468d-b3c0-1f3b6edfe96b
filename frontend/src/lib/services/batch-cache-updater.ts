/**
 * Batch Cache Updater Service
 * Handles staggered batch updates for top 450 symbols (225 per batch, 1-minute intervals)
 * Phase 1: Core Infrastructure
 */

import { getSymbolManager } from "./symbol-manager";
import { getMarketAwareUpdater } from "./market-aware-updater";
import { getEnhancedServerCache } from "./server-cache";

// Dynamic import to avoid server/client issues
let getPricesFromSupabase: any = null;

// Only import server functions in server environment
if (typeof window === "undefined") {
  try {
    const priceModule = require("./supabase-price-service");
    getPricesFromSupabase = priceModule.getPricesFromSupabase;
  } catch (error) {
    console.warn("Could not load supabase-price-service:", error);
  }
}

interface BatchUpdateResult {
  batchIndex: number;
  symbolsRequested: number;
  symbolsUpdated: number;
  duration: number;
  success: boolean;
  error?: string;
}

interface BatchUpdaterStats {
  isRunning: boolean;
  totalBatches: number;
  lastUpdateTimes: (Date | null)[];
  updateResults: BatchUpdateResult[];
  nextScheduledUpdates: Date[];
}

class BatchCacheUpdater {
  private updateIntervals: NodeJS.Timeout[] = [];
  private isUpdating: boolean[] = [];
  private lastUpdateResults: BatchUpdateResult[] = [];
  private isRunning: boolean = false;

  constructor() {
    // Initialize update tracking arrays
    const symbolManager = getSymbolManager();
    const batchCount = Math.ceil(symbolManager.getAllSymbols().length / 225);

    this.isUpdating = new Array(batchCount).fill(false);
    this.lastUpdateResults = [];
  }

  /**
   * Start the batch cache updater with staggered scheduling
   */
  start(): void {
    // Only run on server-side
    if (typeof window !== "undefined") {
      console.log("Batch cache updater can only run on server-side");
      return;
    }

    if (!getPricesFromSupabase) {
      console.log("Batch cache updater dependencies not available");
      return;
    }

    if (this.isRunning) {
      console.log("Batch cache updater already running");
      return;
    }

    console.log("Starting batch cache updater service");
    this.isRunning = true;

    // Set up staggered batch updates
    this.scheduleStaggeredUpdates();
  }

  /**
   * Stop the batch cache updater
   */
  stop(): void {
    console.log("Stopping batch cache updater service");

    // Clear all intervals
    this.updateIntervals.forEach((interval) => {
      if (interval) clearInterval(interval);
    });

    this.updateIntervals = [];
    this.isRunning = false;
  }

  /**
   * Schedule staggered updates for all batches
   */
  private scheduleStaggeredUpdates(): void {
    const symbolManager = getSymbolManager();
    const marketUpdater = getMarketAwareUpdater();
    const batchCount = Math.ceil(symbolManager.getAllSymbols().length / 225);

    console.log(`Scheduling updates for ${batchCount} batches`);

    // Calculate staggered delays
    const staggeredDelays = marketUpdater.calculateStaggeredDelays(batchCount);

    // Schedule each batch with its delay
    for (let batchIndex = 0; batchIndex < batchCount; batchIndex++) {
      const delay = staggeredDelays[batchIndex];

      // Initial delay before starting the interval
      setTimeout(() => {
        // Set up recurring interval for this batch
        const interval = setInterval(() => {
          this.updateBatchIfNeeded(batchIndex);
        }, marketUpdater.getUpdateFrequency() || 1 * 60 * 1000); // Fallback to 1 minute

        this.updateIntervals[batchIndex] = interval;

        // Run initial update
        this.updateBatchIfNeeded(batchIndex);
      }, delay);

      console.log(
        `Batch ${batchIndex} scheduled with ${delay}ms initial delay`
      );
    }

    // Schedule dynamic promotion checks (every hour)
    setInterval(() => {
      this.checkDynamicPromotion();
    }, 60 * 60 * 1000); // 1 hour
  }

  /**
   * Check and perform dynamic symbol promotion
   */
  private async checkDynamicPromotion(): void {
    try {
      const symbolManager = getSymbolManager();
      const cache = getEnhancedServerCache();

      // Get popular symbols from cache
      const popularSymbols = cache.getPopularSymbols(100); // Top 100 most requested

      if (popularSymbols.length === 0) {
        console.log("No popular symbols data available for promotion analysis");
        return;
      }

      // Perform promotion analysis
      const result = await symbolManager.analyzeAndPromoteSymbols(
        popularSymbols
      );

      if (result.promoted.length > 0 || result.demoted.length > 0) {
        console.log(
          `Dynamic promotion completed: ${result.promoted.length} promoted, ${result.demoted.length} demoted`
        );

        // If symbols were changed, restart the updater to use new batches
        if (this.isRunning) {
          console.log("Restarting batch updater with new symbol configuration");
          this.stop();
          setTimeout(() => this.start(), 5000); // Restart after 5 seconds
        }
      }
    } catch (error) {
      console.error("Error during dynamic promotion check:", error);
    }
  }

  /**
   * Update a specific batch if market conditions allow
   */
  private async updateBatchIfNeeded(batchIndex: number): Promise<void> {
    const marketUpdater = getMarketAwareUpdater();

    // Check if updates should be running
    if (!marketUpdater.shouldUpdate()) {
      console.log(`Skipping batch ${batchIndex} update - market closed`);
      return;
    }

    await this.updateBatch(batchIndex);
  }

  /**
   * Update a specific batch of symbols
   */
  async updateBatch(batchIndex: number): Promise<BatchUpdateResult> {
    const startTime = Date.now();
    const symbolManager = getSymbolManager();
    const cache = getEnhancedServerCache();

    // Check if this batch is already updating
    if (this.isUpdating[batchIndex]) {
      console.log(`Batch ${batchIndex} update already in progress, skipping`);
      return {
        batchIndex,
        symbolsRequested: 0,
        symbolsUpdated: 0,
        duration: 0,
        success: false,
        error: "Update already in progress",
      };
    }

    // Safety check
    if (!getPricesFromSupabase) {
      return {
        batchIndex,
        symbolsRequested: 0,
        symbolsUpdated: 0,
        duration: Date.now() - startTime,
        success: false,
        error: "Supabase price service not available",
      };
    }

    this.isUpdating[batchIndex] = true;
    symbolManager.markBatchUpdating(batchIndex, true);

    try {
      // Get symbols for this batch
      const batchSymbols = symbolManager.getBatch(batchIndex);

      if (batchSymbols.length === 0) {
        throw new Error(`No symbols found for batch ${batchIndex}`);
      }

      console.log(
        `Updating batch ${batchIndex}: ${batchSymbols.length} symbols`
      );

      // Fetch fresh data from Supabase
      const freshData = await getPricesFromSupabase(batchSymbols);
      const symbolsUpdated = Object.keys(freshData).length;

      // Update cache with batch data
      cache.setBatch(batchSymbols, freshData, 5 * 60 * 1000); // 5 minute TTL

      const result: BatchUpdateResult = {
        batchIndex,
        symbolsRequested: batchSymbols.length,
        symbolsUpdated,
        duration: Date.now() - startTime,
        success: true,
      };

      // Store result
      this.lastUpdateResults[batchIndex] = result;

      console.log(
        `Batch ${batchIndex} updated successfully: ${symbolsUpdated}/${batchSymbols.length} symbols in ${result.duration}ms`
      );

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";

      const result: BatchUpdateResult = {
        batchIndex,
        symbolsRequested: symbolManager.getBatch(batchIndex).length,
        symbolsUpdated: 0,
        duration: Date.now() - startTime,
        success: false,
        error: errorMessage,
      };

      this.lastUpdateResults[batchIndex] = result;

      console.error(`Batch ${batchIndex} update failed:`, errorMessage);
      return result;
    } finally {
      this.isUpdating[batchIndex] = false;
      symbolManager.markBatchUpdating(batchIndex, false);
    }
  }

  /**
   * Get updater statistics
   */
  getStats(): BatchUpdaterStats {
    const symbolManager = getSymbolManager();
    const marketUpdater = getMarketAwareUpdater();
    const batchCount = Math.ceil(symbolManager.getAllSymbols().length / 225);

    // Calculate next scheduled update times
    const updateFrequency = marketUpdater.getUpdateFrequency();
    const now = new Date();
    const nextScheduledUpdates: Date[] = [];

    for (let i = 0; i < batchCount; i++) {
      const lastResult = this.lastUpdateResults[i];
      if (lastResult && updateFrequency > 0) {
        const lastUpdate = new Date(now.getTime() - lastResult.duration);
        const nextUpdate = new Date(lastUpdate.getTime() + updateFrequency);
        nextScheduledUpdates.push(nextUpdate);
      } else {
        nextScheduledUpdates.push(new Date(0)); // No scheduled update
      }
    }

    return {
      isRunning: this.isRunning,
      totalBatches: batchCount,
      lastUpdateTimes: this.lastUpdateResults.map((result) =>
        result ? new Date(now.getTime() - result.duration) : null
      ),
      updateResults: [...this.lastUpdateResults],
      nextScheduledUpdates,
    };
  }

  /**
   * Force update all batches (for testing/manual refresh)
   */
  async forceUpdateAllBatches(): Promise<BatchUpdateResult[]> {
    const symbolManager = getSymbolManager();
    const batchCount = Math.ceil(symbolManager.getAllSymbols().length / 225);
    const results: BatchUpdateResult[] = [];

    console.log(`Force updating all ${batchCount} batches`);

    for (let i = 0; i < batchCount; i++) {
      const result = await this.updateBatch(i);
      results.push(result);

      // Small delay between batches to avoid overwhelming the database
      if (i < batchCount - 1) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Get health status
   */
  getHealthStatus(): { healthy: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!this.isRunning) {
      issues.push("Updater not running");
    }

    if (!getPricesFromSupabase) {
      issues.push("Supabase price service not available");
    }

    const marketUpdater = getMarketAwareUpdater();
    if (
      !marketUpdater.shouldUpdate() &&
      marketUpdater.getMarketStatus().isOpen
    ) {
      issues.push("Market is open but updates are disabled");
    }

    // Check for recent failures
    const recentFailures = this.lastUpdateResults.filter(
      (result) =>
        result &&
        !result.success &&
        Date.now() - result.duration < 10 * 60 * 1000
    );

    if (recentFailures.length > 0) {
      issues.push(`${recentFailures.length} recent batch update failures`);
    }

    return {
      healthy: issues.length === 0,
      issues,
    };
  }
}

// Singleton instance
let batchCacheUpdaterInstance: BatchCacheUpdater | null = null;

/**
 * Get the batch cache updater instance
 */
export function getBatchCacheUpdater(): BatchCacheUpdater {
  if (!batchCacheUpdaterInstance) {
    batchCacheUpdaterInstance = new BatchCacheUpdater();
  }
  return batchCacheUpdaterInstance;
}

/**
 * Start the batch cache updater service
 */
export function startBatchCacheUpdater(): void {
  const updater = getBatchCacheUpdater();
  updater.start();
}

/**
 * Stop the batch cache updater service
 */
export function stopBatchCacheUpdater(): void {
  const updater = getBatchCacheUpdater();
  updater.stop();
}

/**
 * Reset the batch cache updater (for testing)
 */
export function resetBatchCacheUpdater(): void {
  if (batchCacheUpdaterInstance) {
    batchCacheUpdaterInstance.stop();
  }
  batchCacheUpdaterInstance = null;
}
