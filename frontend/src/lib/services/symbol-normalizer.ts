// Dynamic import to avoid server/client issues
let createActionSupabaseClient: any = null;

// Only import server client in server environment
if (typeof window === "undefined") {
  try {
    const serverModule = require("@/lib/supabase/server");
    createActionSupabaseClient = serverModule.createActionSupabaseClient;
  } catch (error) {
    console.warn("Could not load server supabase client:", error);
  }
}

// Common stock symbol variations
const COMMON_SYMBOLS: Record<string, string> = {
  // Tech
  APPLE: "AAPL",
  MICROSOFT: "MSFT",
  GOOGLE: "GOOGL",
  ALPHABET: "GOOGL",
  AMAZON: "AMZN",
  FACEBOOK: "META",
  "META PLATFORMS": "META",
  NETFLIX: "NFLX",
  TESLA: "TSLA",

  // Finance
  JPMORGAN: "JPM",
  "JP MORGAN": "JPM",
  "BANK OF AMERICA": "BAC",
  "GOLDMAN SACHS": "GS",
  VISA: "V",
  MASTERCARD: "MA",

  // Other
  "COCA COLA": "KO",
  MCDONALDS: "MCD",
  "MCDONALD'S": "MCD",
  WALMART: "WMT",
  DISNEY: "DIS",
  NIKE: "NKE",
};

/**
 * Basic normalization of a stock symbol
 * @param input The input symbol or company name
 * @returns Normalized symbol
 */
export function basicNormalizeSymbol(input: string): string {
  // Trim whitespace and convert to uppercase
  const normalized = input.trim().toUpperCase();

  // Check if it's a common name variation
  if (COMMON_SYMBOLS[normalized]) {
    return COMMON_SYMBOLS[normalized];
  }

  // If it looks like a symbol (1-5 characters, no spaces), return as is
  if (/^[A-Z0-9]{1,5}$/.test(normalized)) {
    return normalized;
  }

  // Otherwise, do our best to convert it to a symbol
  // Remove any non-alphanumeric characters
  return normalized.replace(/[^A-Z0-9]/g, "");
}

/**
 * Look up a symbol in the Supabase database
 * @param input The input symbol or company name
 * @returns The normalized symbol or null if not found
 */
export async function lookupSymbolInDatabase(
  input: string
): Promise<string | null> {
  // Check if server client is available
  if (!createActionSupabaseClient) {
    console.warn(
      "Server Supabase client not available - skipping database lookup"
    );
    return null;
  }

  try {
    const supabase = await createActionSupabaseClient();

    // Query the stock_symbols table
    const { data, error } = await supabase
      .from("stock_symbols")
      .select("normalized_symbol")
      .ilike("input_variant", input)
      .single();

    if (error || !data) {
      return null;
    }

    return data.normalized_symbol;
  } catch (error) {
    console.error("Error looking up symbol in database:", error);
    return null;
  }
}

/**
 * Save a symbol mapping to the database
 * @param input The input variant (what the user entered)
 * @param normalizedSymbol The correct symbol
 * @param displayName Optional display name for the company
 */
export async function saveSymbolToDatabase(
  input: string,
  normalizedSymbol: string,
  displayName?: string
): Promise<boolean> {
  // Check if server client is available
  if (!createActionSupabaseClient) {
    console.warn(
      "Server Supabase client not available - skipping database save"
    );
    return false;
  }

  try {
    const supabase = await createActionSupabaseClient();

    // Insert the mapping
    const { error } = await supabase.from("stock_symbols").insert({
      input_variant: input.trim(),
      normalized_symbol: normalizedSymbol.toUpperCase().trim(),
      display_name: displayName,
    });

    return !error;
  } catch (error) {
    console.error("Error saving symbol to database:", error);
    return false;
  }
}

/**
 * Normalize a stock symbol using all available methods
 * @param input The input symbol or company name
 * @returns The normalized symbol
 */
export async function normalizeSymbol(input: string): Promise<string> {
  if (!input) return "";

  // Try database lookup first
  const dbSymbol = await lookupSymbolInDatabase(input);
  if (dbSymbol) {
    return dbSymbol;
  }

  // Fall back to basic normalization
  const basicSymbol = basicNormalizeSymbol(input);

  // Save this mapping for future use
  await saveSymbolToDatabase(input, basicSymbol);

  return basicSymbol;
}
