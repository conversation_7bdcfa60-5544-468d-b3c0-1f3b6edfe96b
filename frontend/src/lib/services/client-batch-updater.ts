/**
 * Client-Side Batch Cache Updater
 * Handles batch updates for top 450 symbols (225 per batch, 1-minute intervals)
 * Runs in browser to ensure consistent updates
 */

import { getMarketAwareUpdater } from "./market-aware-updater";
import { fetchPrices } from "../api/stock-prices";

interface BatchUpdateResult {
  batchIndex: number;
  symbolsRequested: number;
  symbolsUpdated: number;
  duration: number;
  success: boolean;
  error?: string;
}

class ClientBatchUpdater {
  private updateIntervals: (NodeJS.Timeout | null)[] = [];
  private isRunning = false;
  private isUpdating: boolean[] = [];
  
  // Top 450 symbols (same as server-side)
  private readonly TOP_SYMBOLS = [
    // Major Indices
    "^GSPC", "^DJI", "^IXIC", "^RUT", "^VIX",
    
    // Major ETFs
    "SPY", "QQQ", "IWM", "VTI", "VOO", "VEA", "VWO", "AGG", "BND", "TLT",
    "GLD", "SLV", "USO", "UNG", "XLF", "XLE", "XLK", "XLV", "XLI", "XLP",
    "XLY", "XLU", "XLRE", "XLB", "XME", "XRT", "XHB", "XBI", "XOP", "XAR",
    
    // Major Stocks (S&P 500 top holdings)
    "AAPL", "MSFT", "AMZN", "NVDA", "GOOGL", "GOOG", "META", "TSLA", "BRK.B", "UNH",
    "JNJ", "XOM", "JPM", "V", "PG", "MA", "HD", "CVX", "ABBV", "PFE",
    "KO", "AVGO", "PEP", "TMO", "COST", "WMT", "DIS", "ABT", "ACN", "VZ",
    "ADBE", "NFLX", "CRM", "NKE", "DHR", "TXN", "NEE", "BMY", "PM", "RTX",
    "UPS", "QCOM", "T", "SPGI", "LOW", "HON", "UNP", "IBM", "AMGN", "ELV",
    "SBUX", "CAT", "GS", "AXP", "BLK", "DE", "BKNG", "AMD", "SYK", "TJX",
    "MDLZ", "ADP", "GILD", "CVS", "MMM", "TMUS", "CI", "ZTS", "MO", "SO",
    "PLD", "CB", "DUK", "ITW", "CSX", "EOG", "WM", "CL", "APD", "EMR",
    "SHW", "GD", "NSC", "OXY", "PSA", "AON", "CME", "USB", "PNC", "MSI",
    "ECL", "FCX", "MCK", "SLB", "EQIX", "ICE", "NOC", "F", "GM", "PYPL",
    "COP", "TGT", "REGN", "MPC", "VLO", "PSX", "HUM", "ANTM", "WBA", "KMB",
    "GIS", "SYY", "ADI", "LRCX", "KLAC", "AMAT", "MU", "INTC", "CSCO", "CCI",
    "AMT", "SBAC", "DLR", "O", "WELL", "AVB", "EQR", "ESS", "MAA", "UDR",
    "CPT", "HST", "REG", "BXP", "VTR", "PEAK", "FRT", "KIM", "SPG", "PEI",
    
    // Popular Options Trading Stocks
    "PLTR", "SOFI", "RIVN", "LCID", "NIO", "XPEV", "LI", "BABA", "JD", "PDD",
    "DIDI", "GRAB", "SE", "SHOP", "SQ", "ROKU", "ZOOM", "DOCU", "SNOW", "CRWD",
    "ZS", "OKTA", "DDOG", "NET", "FSLY", "TWLO", "WORK", "UBER", "LYFT", "DASH",
    "ABNB", "COIN", "HOOD", "AFRM", "UPST", "LMND", "ROOT", "OPEN", "RBLX", "U",
    "PINS", "SNAP", "TWTR", "SPOT", "ZM", "PTON", "MRNA", "BNTX", "PFE", "JNJ",
    "NVAX", "OCGN", "INO", "VXRT", "SAVA", "BIIB", "GILD", "REGN", "VRTX", "ILMN",
    
    // Meme Stocks & High Volatility
    "GME", "AMC", "BB", "NOK", "SNDL", "TLRY", "CGC", "ACB", "HEXO", "CRON",
    "WKHS", "RIDE", "NKLA", "HYLN", "GOEV", "CANOO", "FSR", "CCIV", "SPCE", "ARKK",
    "ARKQ", "ARKG", "ARKW", "ARKF", "PRNT", "IZRL", "FINX", "GNOM", "KOIN", "BLOK",
    
    // Energy & Commodities
    "XOM", "CVX", "COP", "EOG", "SLB", "HAL", "BKR", "OXY", "MPC", "VLO",
    "PSX", "HES", "DVN", "FANG", "MRO", "APA", "CLR", "CTRA", "OVV", "SM",
    "RRC", "AR", "CNX", "EQT", "KNTK", "SWN", "CHK", "WLL", "CLF", "X",
    "NUE", "STLD", "RS", "CMC", "MT", "TX", "VALE", "RIO", "BHP", "FCX",
    
    // Financial Sector
    "JPM", "BAC", "WFC", "C", "GS", "MS", "USB", "PNC", "TFC", "COF",
    "AXP", "DFS", "SYF", "ALLY", "RF", "CFG", "HBAN", "FITB", "KEY", "ZION",
    "CMA", "PBCT", "MTB", "STI", "BBT", "SIVB", "PACW", "WAL", "SBNY", "CIT",
    
    // Technology Sector
    "AAPL", "MSFT", "GOOGL", "GOOG", "META", "AMZN", "TSLA", "NVDA", "AMD", "INTC",
    "CSCO", "ORCL", "IBM", "QCOM", "TXN", "ADI", "LRCX", "KLAC", "AMAT", "MU",
    "MRVL", "XLNX", "SWKS", "QRVO", "MPWR", "MCHP", "RMBS", "CRUS", "SLAB", "DIOD",
    
    // Healthcare & Biotech
    "JNJ", "PFE", "UNH", "ABBV", "TMO", "ABT", "DHR", "BMY", "AMGN", "GILD",
    "REGN", "VRTX", "BIIB", "ILMN", "MRNA", "BNTX", "NVAX", "OCGN", "INO", "VXRT",
    "SAVA", "ADUCANUMAB", "SAGE", "IONS", "EXAS", "VEEV", "DXCM", "ISRG", "ALGN", "HOLX",
    
    // Consumer & Retail
    "AMZN", "WMT", "HD", "TGT", "COST", "LOW", "TJX", "SBUX", "MCD", "NKE",
    "DIS", "NFLX", "ROKU", "SPOT", "ZM", "PTON", "LULU", "ULTA", "EL", "PG",
    "KO", "PEP", "CL", "KMB", "GIS", "K", "CPB", "CAG", "SJM", "MKC"
  ];

  private readonly BATCH_SIZE = 225;

  /**
   * Start the client-side batch updater
   */
  start(): void {
    if (this.isRunning) {
      console.log("Client batch updater already running");
      return;
    }

    console.log("Starting client-side batch cache updater");
    this.isRunning = true;

    // Calculate number of batches
    const batchCount = Math.ceil(this.TOP_SYMBOLS.length / this.BATCH_SIZE);
    console.log(`Scheduling ${batchCount} batches of ${this.BATCH_SIZE} symbols each`);

    // Schedule staggered batch updates
    this.scheduleStaggeredUpdates(batchCount);
  }

  /**
   * Stop the batch updater
   */
  stop(): void {
    console.log("Stopping client-side batch cache updater");
    
    this.updateIntervals.forEach((interval) => {
      if (interval) clearInterval(interval);
    });
    
    this.updateIntervals = [];
    this.isRunning = false;
  }

  /**
   * Schedule staggered updates for all batches
   */
  private scheduleStaggeredUpdates(batchCount: number): void {
    const marketUpdater = getMarketAwareUpdater();
    
    // Calculate staggered delays (spread batches evenly across 1 minute)
    const baseDelay = 60000 / batchCount; // Spread across 1 minute
    
    for (let batchIndex = 0; batchIndex < batchCount; batchIndex++) {
      const delay = batchIndex * baseDelay;
      
      // Initial delay before starting the interval
      setTimeout(() => {
        // Set up recurring interval for this batch (every 2 minutes)
        const interval = setInterval(() => {
          this.updateBatchIfNeeded(batchIndex);
        }, 2 * 60 * 1000); // 2 minutes for full cycle
        
        this.updateIntervals[batchIndex] = interval;
        
        // Run initial update
        this.updateBatchIfNeeded(batchIndex);
      }, delay);
      
      console.log(`Batch ${batchIndex} scheduled with ${delay}ms initial delay, 2-minute intervals`);
    }
  }

  /**
   * Update batch if market conditions allow
   */
  private async updateBatchIfNeeded(batchIndex: number): Promise<void> {
    const marketUpdater = getMarketAwareUpdater();
    const schedule = marketUpdater.getUpdateSchedule();
    
    if (!schedule.shouldUpdate) {
      console.log(`Skipping batch ${batchIndex} update: ${schedule.reason}`);
      return;
    }
    
    await this.updateBatch(batchIndex);
  }

  /**
   * Update a specific batch of symbols
   */
  private async updateBatch(batchIndex: number): Promise<BatchUpdateResult> {
    const startTime = Date.now();
    
    // Check if this batch is already updating
    if (this.isUpdating[batchIndex]) {
      console.log(`Batch ${batchIndex} update already in progress, skipping`);
      return {
        batchIndex,
        symbolsRequested: 0,
        symbolsUpdated: 0,
        duration: 0,
        success: false,
        error: "Update already in progress",
      };
    }
    
    this.isUpdating[batchIndex] = true;
    
    try {
      // Get symbols for this batch
      const startIdx = batchIndex * this.BATCH_SIZE;
      const endIdx = Math.min(startIdx + this.BATCH_SIZE, this.TOP_SYMBOLS.length);
      const batchSymbols = this.TOP_SYMBOLS.slice(startIdx, endIdx);
      
      if (batchSymbols.length === 0) {
        throw new Error(`No symbols found for batch ${batchIndex}`);
      }
      
      console.log(`Updating batch ${batchIndex}: ${batchSymbols.length} symbols`);
      
      // Fetch fresh data from Supabase via our API
      const freshData = await fetchPrices(batchSymbols, true); // Force refresh
      const symbolsUpdated = Object.keys(freshData).length;
      
      const result: BatchUpdateResult = {
        batchIndex,
        symbolsRequested: batchSymbols.length,
        symbolsUpdated,
        duration: Date.now() - startTime,
        success: true,
      };
      
      console.log(`Batch ${batchIndex} completed: ${symbolsUpdated}/${batchSymbols.length} symbols updated in ${result.duration}ms`);
      
      return result;
    } catch (error) {
      const result: BatchUpdateResult = {
        batchIndex,
        symbolsRequested: 0,
        symbolsUpdated: 0,
        duration: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
      
      console.error(`Batch ${batchIndex} failed:`, error);
      return result;
    } finally {
      this.isUpdating[batchIndex] = false;
    }
  }

  /**
   * Force update all batches (for manual refresh)
   */
  async forceUpdateAllBatches(): Promise<BatchUpdateResult[]> {
    const batchCount = Math.ceil(this.TOP_SYMBOLS.length / this.BATCH_SIZE);
    const results: BatchUpdateResult[] = [];
    
    console.log(`Force updating all ${batchCount} batches`);
    
    for (let i = 0; i < batchCount; i++) {
      const result = await this.updateBatch(i);
      results.push(result);
      
      // Small delay between batches to avoid overwhelming the API
      if (i < batchCount - 1) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }
    
    return results;
  }

  /**
   * Get current status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeBatches: this.updateIntervals.filter(interval => interval !== null).length,
      totalSymbols: this.TOP_SYMBOLS.length,
      batchSize: this.BATCH_SIZE,
      batchCount: Math.ceil(this.TOP_SYMBOLS.length / this.BATCH_SIZE),
    };
  }
}

// Singleton instance
let clientBatchUpdaterInstance: ClientBatchUpdater | null = null;

/**
 * Get the client batch updater instance
 */
export function getClientBatchUpdater(): ClientBatchUpdater {
  if (!clientBatchUpdaterInstance) {
    clientBatchUpdaterInstance = new ClientBatchUpdater();
  }
  return clientBatchUpdaterInstance;
}

/**
 * Start the client batch updater service
 */
export function startClientBatchUpdater(): void {
  const updater = getClientBatchUpdater();
  updater.start();
}

/**
 * Stop the client batch updater service
 */
export function stopClientBatchUpdater(): void {
  const updater = getClientBatchUpdater();
  updater.stop();
}
