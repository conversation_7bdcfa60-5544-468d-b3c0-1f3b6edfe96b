/**
 * Symbol Manager Service
 * Manages the top 450 symbols for batch cache updates
 * Phase 1: Core Infrastructure
 */

// Import symbol lists from existing initialization script
const majorIndices = [
  "^GSPC", // S&P 500
  "^DJI", // Dow Jones Industrial Average
  "^IXIC", // NASDAQ Composite
  "^RUT", // Russell 2000
  "^VIX", // CBOE Volatility Index
  "^FTSE", // FTSE 100
  "^N225", // Nikkei 225
  "^HSI", // Hang Seng Index
  "^GDAXI", // DAX
  "^FCHI", // CAC 40
];

const popularETFs = [
  "SPY",
  "QQQ",
  "IWM",
  "VTI",
  "VOO",
  "ARKK",
  "XLF",
  "XLE",
  "GLD",
  "SLV",
  "USO",
  "TLT",
  "LQD",
  "HYG",
  "VXX",
  "EFA",
  "EEM",
  "FXI",
  "KWEB",
  "JETS",
  "XBI",
  "IBB",
  "XRT",
  "XHB",
  "XME",
  "XOP",
  "KRE",
  "SMH",
  "SOXX",
  "ARKQ",
];

const popularFutures = [
  "ES=F",
  "NQ=F",
  "YM=F",
  "RTY=F",
  "GC=F",
  "SI=F",
  "CL=F",
  "NG=F",
  "ZB=F",
  "ZN=F",
  "ZF=F",
  "ZT=F",
  "6E=F",
  "6J=F",
  "6B=F",
  "6A=F",
  "DX=F",
  "BTC=F",
  "ETH=F",
  "HG=F",
];

// Top S&P 500 stocks (from your existing list)
const topSP500Stocks = [
  "AAPL",
  "MSFT",
  "AMZN",
  "GOOGL",
  "GOOG",
  "META",
  "TSLA",
  "NVDA",
  "BRK-B",
  "JPM",
  "JNJ",
  "V",
  "PG",
  "UNH",
  "HD",
  "MA",
  "BAC",
  "DIS",
  "ADBE",
  "CRM",
  "CMCSA",
  "XOM",
  "VZ",
  "NFLX",
  "INTC",
  "PFE",
  "ABT",
  "KO",
  "PEP",
  "T",
  "CSCO",
  "WMT",
  "MRK",
  "CVX",
  "NKE",
  "TMO",
  "ABBV",
  "AVGO",
  "ACN",
  "PYPL",
  "MCD",
  "DHR",
  "NEE",
  "LLY",
  "COST",
  "MDT",
  "UNP",
  "BMY",
  "PM",
  "HON",
  "TXN",
  "QCOM",
  "LIN",
  "ORCL",
  "AMT",
  "IBM",
  "SBUX",
  "MMM",
  "C",
  "BA",
  "RTX",
  "GS",
  "LOW",
  "INTU",
  "BLK",
  "CHTR",
  "GILD",
  "MDLZ",
  "ISRG",
  "SPGI",
  "MO",
  "TGT",
  "BKNG",
  "AXP",
  "PLD",
  "AMGN",
  "CVS",
  "CI",
  "ANTM",
  "COP",
  "CAT",
  "TJX",
  "ZTS",
  "USB",
  "FIS",
  "TMUS",
  "MS",
  "SCHW",
  "CME",
  "BDX",
  "CCI",
  "PNC",
  "CB",
  "DUK",
  "SO",
  "ICE",
  "D",
  "EQIX",
  "SYK",
  "VRTX",
  "NSC",
  "AON",
  "BSX",
  "MMC",
  "CSX",
  "ITW",
  "NOC",
  "EMR",
  "FCX",
  "PSA",
  "WM",
  "MCO",
  "FDX",
  "GM",
  "F",
  "DELL",
  "HPQ",
  "ORCL",
  "CRM",
  "NOW",
  "SNOW",
  "DDOG",
  "NET",
  "CRWD",
  "ZS",
  "OKTA",
  "TWLO",
  "SHOP",
  "SQ",
  "UBER",
  "LYFT",
  "ABNB",
  "DASH",
  "COIN",
  "RBLX",
  "HOOD",
  "RIVN",
  "LCID",
  "NIO",
  "XPEV",
  "LI",
  "BABA",
  "JD",
  "PDD",
  "BIDU",
  "NTES",
  "TME",
  "BILI",
  "IQ",
  "VIPS",
  // Continue with more top S&P 500 stocks to reach ~370 stocks
  "AMD",
  "NVDA",
  "QCOM",
  "AVGO",
  "MRVL",
  "ADI",
  "KLAC",
  "LRCX",
  "AMAT",
  "MU",
  "NXPI",
  "MCHP",
  "SWKS",
  "QRVO",
  "MPWR",
  "MXIM",
  "XLNX",
  "ALGN",
  "IDXX",
  "ILMN",
  "REGN",
  "BIIB",
  "MRNA",
  "BNTX",
  "NVAX",
  "CRSP",
  "EDIT",
  "NTLA",
  "BEAM",
  "VERV",
  "ARKG",
  "ARKF",
  "ARKW",
  "PRNT",
  "IZRL",
  "FINX",
  "CIBR",
  "HACK",
  "BUG",
  "ROBO",
  "BOTZ",
  "IRBO",
  "THNQ",
  "NERD",
  "ESPO",
  "HERO",
  "UFO",
  "MOON",
  "SPCE",
  "ARKX",
  "PBW",
  "ICLN",
  "QCLN",
  "SMOG",
  "FAN",
  "TAN",
  "LIT",
  "BATT",
  "GRID",
  "ACES",
  "REMX",
  "PICK",
  "SIL",
  "COPX",
  "GUNR",
  "DBB",
  "DBA",
  "CORN",
  "SOYB",
  "WEAT",
  "CANE",
  "JO",
  "NIB",
  "BAL",
  "COW",
  "MILK",
  "CAFE",
  "TAGS",
  "FTXG",
  "FTXD",
  "FTXL",
  "FTXH",
  "FTXR",
  "FTXN",
  "FTXO",
  "SPHD",
  "SPHQ",
  "SPYG",
  "SPYV",
  "RSP",
  "EQL",
  "SPLG",
  "SPTM",
  "SPTI",
  "SPTS",
  "GOVT",
  "FLOT",
  "MINT",
  "SHY",
  "IEI",
  "IEF",
  "TLH",
  "VGIT",
  "VGLT",
  "SCHZ",
  "SCHR",
  "SCHO",
  "VTEB",
  "MUB",
  "HYD",
  "PFF",
  "PFFD",
  "KBWY",
  "REM",
  "MORT",
  "VMBS",
  "MBB",
  "CMBS",
  "BKLN",
  "SRLN",
  "FLRN",
  "TFLO",
  "JAAA",
  "NEAR",
  "JPST",
  "GSST",
  "ICSH",
  "BIL",
  "SHV",
  "SGOV",
  "SPSB",
  "SPIB",
  "SPDW",
  "SPEM",
  "SPYG",
  "MTUM",
  "QUAL",
  "USMV",
  "VLUE",
  "SIZE",
];

interface SymbolBatch {
  batchIndex: number;
  symbols: string[];
  lastUpdated: Date | null;
  updateInProgress: boolean;
}

interface SymbolStats {
  totalSymbols: number;
  batchCount: number;
  symbolsPerBatch: number;
  lastFullUpdate: Date | null;
  promotedSymbols: number;
  demotedSymbols: number;
  lastPromotionCheck: Date | null;
}

interface PromotionCandidate {
  symbol: string;
  requestCount: number;
  requestsPerHour: number;
  lastRequested: Date;
  isTopSymbol: boolean;
}

class SymbolManager {
  private topSymbols: string[] = [];
  private batches: SymbolBatch[] = [];
  private readonly BATCH_SIZE = 150;
  private readonly TARGET_SYMBOL_COUNT = 450;

  // Dynamic promotion settings
  private readonly PROMOTION_THRESHOLD = 10; // requests per hour
  private readonly DEMOTION_THRESHOLD = 1; // requests per hour
  private readonly CHECK_INTERVAL = 60 * 60 * 1000; // 1 hour
  private promotedCount = 0;
  private demotedCount = 0;
  private lastPromotionCheck: Date | null = null;

  constructor() {
    this.initializeTopSymbols();
  }

  /**
   * Initialize the top 450 symbols from existing lists
   */
  private initializeTopSymbols(): void {
    console.log("Initializing top 450 symbols for batch cache...");

    // Combine all symbol lists with priority order
    const allSymbols = [
      ...majorIndices, // ~10 symbols (highest priority)
      ...popularETFs, // ~30 symbols
      ...popularFutures, // ~20 symbols
      ...topSP500Stocks, // ~370+ symbols
    ];

    // Remove duplicates while preserving order
    const uniqueSymbols = [...new Set(allSymbols)];

    // Take top 450 symbols
    this.topSymbols = uniqueSymbols.slice(0, this.TARGET_SYMBOL_COUNT);

    console.log(`Selected ${this.topSymbols.length} symbols for batch cache`);
    console.log(
      `Indices: ${majorIndices.length}, ETFs: ${popularETFs.length}, Futures: ${popularFutures.length}`
    );

    // Create batches
    this.createBatches();
  }

  /**
   * Create batches of symbols for staggered updates
   */
  private createBatches(): void {
    this.batches = [];

    for (let i = 0; i < this.topSymbols.length; i += this.BATCH_SIZE) {
      const batchSymbols = this.topSymbols.slice(i, i + this.BATCH_SIZE);

      this.batches.push({
        batchIndex: Math.floor(i / this.BATCH_SIZE),
        symbols: batchSymbols,
        lastUpdated: null,
        updateInProgress: false,
      });
    }

    console.log(
      `Created ${this.batches.length} batches of ${this.BATCH_SIZE} symbols each`
    );
  }

  /**
   * Get a specific batch by index
   */
  getBatch(batchIndex: number): string[] {
    if (batchIndex < 0 || batchIndex >= this.batches.length) {
      return [];
    }
    return this.batches[batchIndex].symbols;
  }

  /**
   * Get all top symbols
   */
  getAllSymbols(): string[] {
    return [...this.topSymbols];
  }

  /**
   * Get batch information
   */
  getBatchInfo(batchIndex: number): SymbolBatch | null {
    if (batchIndex < 0 || batchIndex >= this.batches.length) {
      return null;
    }
    return { ...this.batches[batchIndex] };
  }

  /**
   * Mark batch as being updated
   */
  markBatchUpdating(batchIndex: number, isUpdating: boolean): void {
    if (batchIndex >= 0 && batchIndex < this.batches.length) {
      this.batches[batchIndex].updateInProgress = isUpdating;
      if (!isUpdating) {
        this.batches[batchIndex].lastUpdated = new Date();
      }
    }
  }

  /**
   * Get statistics about symbol management
   */
  getStats(): SymbolStats {
    const lastUpdates = this.batches
      .map((b) => b.lastUpdated)
      .filter((date) => date !== null) as Date[];

    return {
      totalSymbols: this.topSymbols.length,
      batchCount: this.batches.length,
      symbolsPerBatch: this.BATCH_SIZE,
      lastFullUpdate:
        lastUpdates.length > 0
          ? new Date(Math.min(...lastUpdates.map((d) => d.getTime())))
          : null,
      promotedSymbols: this.promotedCount,
      demotedSymbols: this.demotedCount,
      lastPromotionCheck: this.lastPromotionCheck,
    };
  }

  /**
   * Check if a symbol is in the top 450 list
   */
  isTopSymbol(symbol: string): boolean {
    return this.topSymbols.includes(symbol.toUpperCase());
  }

  /**
   * Get the batch index for a specific symbol
   */
  getBatchIndexForSymbol(symbol: string): number {
    const symbolIndex = this.topSymbols.indexOf(symbol.toUpperCase());
    if (symbolIndex === -1) return -1;
    return Math.floor(symbolIndex / this.BATCH_SIZE);
  }

  /**
   * Check if dynamic promotion should run
   */
  shouldCheckPromotion(): boolean {
    if (!this.lastPromotionCheck) return true;
    return Date.now() - this.lastPromotionCheck.getTime() > this.CHECK_INTERVAL;
  }

  /**
   * Analyze symbol popularity and perform promotions/demotions
   */
  async analyzeAndPromoteSymbols(
    popularSymbols: any[]
  ): Promise<{ promoted: string[]; demoted: string[] }> {
    if (!this.shouldCheckPromotion()) {
      return { promoted: [], demoted: [] };
    }

    console.log("Running dynamic symbol promotion analysis...");
    this.lastPromotionCheck = new Date();

    const promoted: string[] = [];
    const demoted: string[] = [];

    // Calculate requests per hour for each symbol
    const candidates: PromotionCandidate[] = popularSymbols.map((symbol) => {
      const hoursSinceLastRequest =
        (Date.now() - new Date(symbol.lastRequested).getTime()) /
        (1000 * 60 * 60);
      const requestsPerHour =
        hoursSinceLastRequest > 0
          ? symbol.requestCount / Math.max(hoursSinceLastRequest, 0.1)
          : symbol.requestCount;

      return {
        symbol: symbol.symbol,
        requestCount: symbol.requestCount,
        requestsPerHour,
        lastRequested: new Date(symbol.lastRequested),
        isTopSymbol: this.isTopSymbol(symbol.symbol),
      };
    });

    // Find promotion candidates (non-top symbols with high activity)
    const promotionCandidates = candidates
      .filter(
        (c) => !c.isTopSymbol && c.requestsPerHour >= this.PROMOTION_THRESHOLD
      )
      .sort((a, b) => b.requestsPerHour - a.requestsPerHour)
      .slice(0, 5); // Max 5 promotions per check

    // Find demotion candidates (top symbols with low activity)
    const demotionCandidates = candidates
      .filter(
        (c) => c.isTopSymbol && c.requestsPerHour <= this.DEMOTION_THRESHOLD
      )
      .sort((a, b) => a.requestsPerHour - b.requestsPerHour)
      .slice(0, 5); // Max 5 demotions per check

    // Perform promotions and demotions
    const promotionsNeeded = Math.min(
      promotionCandidates.length,
      demotionCandidates.length
    );

    for (let i = 0; i < promotionsNeeded; i++) {
      const toPromote = promotionCandidates[i];
      const toDemote = demotionCandidates[i];

      // Replace demoted symbol with promoted symbol
      const demoteIndex = this.topSymbols.indexOf(toDemote.symbol);
      if (demoteIndex !== -1) {
        this.topSymbols[demoteIndex] = toPromote.symbol;
        promoted.push(toPromote.symbol);
        demoted.push(toDemote.symbol);

        console.log(
          `Promoted ${toPromote.symbol} (${toPromote.requestsPerHour.toFixed(
            1
          )} req/hr) replacing ${
            toDemote.symbol
          } (${toDemote.requestsPerHour.toFixed(1)} req/hr)`
        );
      }
    }

    // Update counters
    this.promotedCount += promoted.length;
    this.demotedCount += demoted.length;

    // Recreate batches if changes were made
    if (promoted.length > 0 || demoted.length > 0) {
      this.createBatches();
      console.log(
        `Dynamic promotion complete: ${promoted.length} promoted, ${demoted.length} demoted`
      );
    }

    return { promoted, demoted };
  }

  /**
   * Manually promote a symbol to top list
   */
  manualPromoteSymbol(symbol: string): boolean {
    const upperSymbol = symbol.toUpperCase();

    if (this.isTopSymbol(upperSymbol)) {
      return false; // Already in top list
    }

    if (this.topSymbols.length >= this.TARGET_SYMBOL_COUNT) {
      // Remove least popular symbol (last in list)
      const demoted = this.topSymbols.pop();
      console.log(
        `Manually demoted ${demoted} to make room for ${upperSymbol}`
      );
      this.demotedCount++;
    }

    // Add to beginning of list (highest priority)
    this.topSymbols.unshift(upperSymbol);
    this.promotedCount++;

    // Recreate batches
    this.createBatches();

    console.log(`Manually promoted ${upperSymbol} to top symbols list`);
    return true;
  }

  /**
   * Manually demote a symbol from top list
   */
  manualDemoteSymbol(symbol: string): boolean {
    const upperSymbol = symbol.toUpperCase();
    const index = this.topSymbols.indexOf(upperSymbol);

    if (index === -1) {
      return false; // Not in top list
    }

    // Remove from top list
    this.topSymbols.splice(index, 1);
    this.demotedCount++;

    // Recreate batches
    this.createBatches();

    console.log(`Manually demoted ${upperSymbol} from top symbols list`);
    return true;
  }
}

// Singleton instance
let symbolManagerInstance: SymbolManager | null = null;

/**
 * Get the symbol manager instance
 */
export function getSymbolManager(): SymbolManager {
  if (!symbolManagerInstance) {
    symbolManagerInstance = new SymbolManager();
  }
  return symbolManagerInstance;
}

/**
 * Reset the symbol manager (for testing)
 */
export function resetSymbolManager(): void {
  symbolManagerInstance = null;
}
