/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Service for fetching and caching stock prices from the centralized price system
 */

// Cache structure
interface PriceCache {
  [symbol: string]: {
    price: number;
    previousClose: number;
    change: number;
    changePercent: number;
    lastUpdated: string;
    name?: string; // Name of the underlying security
    cachedAt: number; // Timestamp when the price was cached
  };
}

// Cache TTL in milliseconds
const CACHE_TTL = 60000; // 1 minute

// In-memory cache
let priceCache: PriceCache = {};

/**
 * Fetch prices for multiple symbols
 * @param symbols Array of stock symbols
 * @param forceRefresh Whether to bypass the cache and force a refresh
 * @returns Object mapping symbols to price data
 */
export async function fetchPrices(
  symbols: string[],
  forceRefresh: boolean = false
): Promise<Record<string, any>> {
  if (!symbols || symbols.length === 0) {
    return {};
  }

  // Deduplicate symbols
  const uniqueSymbols = [...new Set(symbols)];

  // Check which symbols need to be fetched
  const now = Date.now();
  const symbolsToFetch = forceRefresh
    ? uniqueSymbols
    : uniqueSymbols.filter((symbol) => {
        const cached = priceCache[symbol];
        return !cached || now - cached.cachedAt > CACHE_TTL;
      });

  // If we need to fetch any symbols
  if (symbolsToFetch.length > 0) {
    try {
      // Generate session ID for tracking
      const sessionId = `client-${Date.now()}-${Math.random()}`;

      // Fetch from API
      const response = await fetch(
        `/api/prices?symbols=${symbolsToFetch.join(",")}`,
        {
          headers: {
            "x-session-id": sessionId,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();

      // Update cache with new data
      if (data.prices) {
        Object.entries(data.prices).forEach(
          ([symbol, priceData]: [string, any]) => {
            priceCache[symbol] = {
              ...priceData,
              cachedAt: now,
            };
          }
        );
      }
    } catch (error) {
      console.error("Error fetching prices:", error);
      // Continue with cached data if available
    }
  }

  // Return data for all requested symbols (from cache)
  const result: Record<string, any> = {};

  uniqueSymbols.forEach((symbol) => {
    if (priceCache[symbol]) {
      result[symbol] = {
        price: priceCache[symbol].price,
        previousClose: priceCache[symbol].previousClose,
        change: priceCache[symbol].change,
        changePercent: priceCache[symbol].changePercent,
        lastUpdated: priceCache[symbol].lastUpdated,
        name: priceCache[symbol].name, // Include the name
      };
    } else {
      // No data available for this symbol
      result[symbol] = null;
    }
  });

  return result;
}

/**
 * Fetch price for a single symbol
 * @param symbol Stock symbol
 * @param forceRefresh Whether to bypass the cache and force a refresh
 * @returns Price data or null if not available
 */
export async function fetchPrice(
  symbol: string,
  forceRefresh: boolean = false
): Promise<any | null> {
  if (!symbol) {
    return null;
  }

  const prices = await fetchPrices([symbol], forceRefresh);
  return prices[symbol];
}

/**
 * Clear the price cache
 */
export function clearPriceCache(): void {
  priceCache = {};
}

/**
 * Get cache statistics
 */
export function getCacheStats(): { size: number; oldestEntry: number | null } {
  const symbols = Object.keys(priceCache);

  if (symbols.length === 0) {
    return { size: 0, oldestEntry: null };
  }

  const now = Date.now();
  const oldestEntry = Math.min(
    ...Object.values(priceCache).map((entry) => now - entry.cachedAt)
  );

  return {
    size: symbols.length,
    oldestEntry,
  };
}
