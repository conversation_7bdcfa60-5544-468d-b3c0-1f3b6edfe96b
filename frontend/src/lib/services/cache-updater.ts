/**
 * Cache Updater Service
 * Periodically updates the global symbol cache with fresh data
 */

// Only import these in server environment
let getGlobalSymbolCache: any = null;
let getPricesFromSupabase: any = null;

// Dynamic imports for server-side only
if (typeof window === "undefined") {
  // Server-side imports
  try {
    const cacheModule = require("./global-symbol-cache");
    getGlobalSymbolCache = cacheModule.getGlobalSymbolCache;
  } catch (error) {
    console.warn("Could not load global-symbol-cache on server:", error);
  }

  try {
    const priceModule = require("./supabase-price-service");
    getPricesFromSupabase = priceModule.getPricesFromSupabase;
  } catch (error) {
    console.warn("Could not load supabase-price-service on server:", error);
  }
}

class CacheUpdaterService {
  private updateInterval: NodeJS.Timeout | null = null;
  private isUpdating = false;
  private readonly UPDATE_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private readonly BATCH_SIZE = 100;

  /**
   * Start the cache updater service
   */
  start(): void {
    // Only run on server-side
    if (typeof window !== "undefined") {
      console.log("Cache updater can only run on server-side");
      return;
    }

    if (!getGlobalSymbolCache || !getPricesFromSupabase) {
      console.log("Cache updater dependencies not available");
      return;
    }

    if (this.updateInterval) {
      console.log("Cache updater already running");
      return;
    }

    console.log("Starting cache updater service");

    // Run initial update
    this.updateCache();

    // Set up periodic updates
    this.updateInterval = setInterval(() => {
      this.updateCache();
    }, this.UPDATE_INTERVAL);
  }

  /**
   * Stop the cache updater service
   */
  stop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      console.log("Cache updater service stopped");
    }
  }

  /**
   * Check if market is open (US market hours)
   */
  private isMarketOpen(): boolean {
    const now = new Date();
    const utcHour = now.getUTCHours();
    const utcDay = now.getUTCDay();

    // US market hours: 9:30 AM - 4:00 PM EST (14:30 - 21:00 UTC)
    // Monday (1) to Friday (5)
    return utcDay >= 1 && utcDay <= 5 && utcHour >= 14 && utcHour < 21;
  }

  /**
   * Update the cache with fresh data
   */
  private async updateCache(): Promise<void> {
    if (this.isUpdating) {
      console.log("Cache update already in progress, skipping");
      return;
    }

    // Safety checks
    if (!getGlobalSymbolCache || !getPricesFromSupabase) {
      console.log("Cache update dependencies not available");
      return;
    }

    // Only update during market hours
    if (!this.isMarketOpen()) {
      console.log("Market is closed, skipping cache update");
      return;
    }

    this.isUpdating = true;

    try {
      const globalCache = getGlobalSymbolCache();
      const staleSymbols = globalCache.getStaleSymbols();

      if (staleSymbols.length === 0) {
        console.log("No stale symbols to update");
        return;
      }

      console.log(`Updating ${staleSymbols.length} stale symbols in cache`);

      // Process symbols in batches
      for (let i = 0; i < staleSymbols.length; i += this.BATCH_SIZE) {
        const batch = staleSymbols.slice(i, i + this.BATCH_SIZE);

        try {
          const freshData = await getPricesFromSupabase(batch);
          globalCache.updateCache(freshData);

          console.log(`Updated batch of ${batch.length} symbols`);

          // Small delay between batches to avoid overwhelming the database
          if (i + this.BATCH_SIZE < staleSymbols.length) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        } catch (error) {
          console.error(
            `Error updating batch ${i}-${i + batch.length}:`,
            error
          );
          // Continue with next batch
        }
      }

      // Log cache statistics
      const stats = globalCache.getStats();
      console.log("Cache update completed:", {
        totalSymbols: stats.totalSymbols,
        totalSubscribers: stats.totalSubscribers,
        cacheHitRate: `${stats.cacheHitRate.toFixed(1)}%`,
      });
    } catch (error) {
      console.error("Error in cache update process:", error);
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * Force an immediate cache update
   */
  async forceUpdate(): Promise<void> {
    console.log("Forcing cache update");
    await this.updateCache();
  }

  /**
   * Get update service status
   */
  getStatus(): {
    isRunning: boolean;
    isUpdating: boolean;
    nextUpdate: string | null;
  } {
    return {
      isRunning: this.updateInterval !== null,
      isUpdating: this.isUpdating,
      nextUpdate: this.updateInterval
        ? new Date(Date.now() + this.UPDATE_INTERVAL).toISOString()
        : null,
    };
  }
}

// Singleton instance
let cacheUpdaterInstance: CacheUpdaterService | null = null;

export function getCacheUpdater(): CacheUpdaterService {
  if (!cacheUpdaterInstance) {
    cacheUpdaterInstance = new CacheUpdaterService();
  }
  return cacheUpdaterInstance;
}

export function startCacheUpdater(): void {
  const updater = getCacheUpdater();
  updater.start();
}

export function stopCacheUpdater(): void {
  if (cacheUpdaterInstance) {
    cacheUpdaterInstance.stop();
  }
}

// Auto-start disabled for now to avoid server/client issues
// Will be enabled once the system is stable
// if (typeof window === "undefined" && process.env.NODE_ENV === "production") {
//   // Only start in server environment
//   setTimeout(() => {
//     startCacheUpdater();
//   }, 5000); // Start after 5 seconds to allow app initialization
// }
