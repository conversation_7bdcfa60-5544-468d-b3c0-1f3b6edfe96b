/**
 * Global Symbol Cache Service
 * Implements shared caching to reduce duplicate API calls for the same symbols across multiple users
 * Client-side only implementation
 */

// Use the existing PriceData interface from price-context
interface PriceData {
  price: number;
  previousClose: number;
  change: number;
  changePercent: number;
  lastUpdated: string;
  name?: string;
}

interface CacheEntry {
  data: PriceData;
  lastFetched: number;
  subscribers: Set<string>; // User session IDs
  isStale: boolean;
}

interface SubscriptionCallback {
  (symbol: string, data: PriceData): void;
}

class GlobalSymbolCacheService {
  private cache = new Map<string, CacheEntry>();
  private subscriptions = new Map<string, Set<SubscriptionCallback>>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly STALE_THRESHOLD = 2 * 60 * 1000; // 2 minutes
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    // Start cleanup process
    this.startCleanup();
  }

  /**
   * Subscribe to symbol updates
   */
  subscribe(
    sessionId: string,
    symbols: string[],
    callback: SubscriptionCallback
  ): void {
    symbols.forEach((symbol) => {
      // Add to cache if not exists
      if (!this.cache.has(symbol)) {
        this.cache.set(symbol, {
          data: {} as PriceData,
          lastFetched: 0,
          subscribers: new Set(),
          isStale: true,
        });
      }

      // Add subscriber
      const entry = this.cache.get(symbol)!;
      entry.subscribers.add(sessionId);

      // Add callback subscription
      if (!this.subscriptions.has(symbol)) {
        this.subscriptions.set(symbol, new Set());
      }
      this.subscriptions.get(symbol)!.add(callback);
    });
  }

  /**
   * Unsubscribe from symbol updates
   */
  unsubscribe(
    sessionId: string,
    symbols: string[],
    callback?: SubscriptionCallback
  ): void {
    symbols.forEach((symbol) => {
      const entry = this.cache.get(symbol);
      if (entry) {
        entry.subscribers.delete(sessionId);

        // Remove callback if provided
        if (callback) {
          const callbacks = this.subscriptions.get(symbol);
          if (callbacks) {
            callbacks.delete(callback);
            if (callbacks.size === 0) {
              this.subscriptions.delete(symbol);
            }
          }
        }

        // Remove cache entry if no subscribers
        if (entry.subscribers.size === 0) {
          this.cache.delete(symbol);
          this.subscriptions.delete(symbol);
        }
      }
    });
  }

  /**
   * Get cached data for symbols
   */
  getCachedData(symbols: string[]): {
    cached: Record<string, PriceData>;
    missing: string[];
  } {
    const cached: Record<string, PriceData> = {};
    const missing: string[] = [];
    const now = Date.now();

    symbols.forEach((symbol) => {
      const entry = this.cache.get(symbol);
      if (entry && !entry.isStale && now - entry.lastFetched < this.CACHE_TTL) {
        cached[symbol] = entry.data;
      } else {
        missing.push(symbol);
      }
    });

    return { cached, missing };
  }

  /**
   * Update cache with fresh data
   */
  updateCache(symbolData: Record<string, PriceData>): void {
    const now = Date.now();

    Object.entries(symbolData).forEach(([symbol, data]) => {
      const entry = this.cache.get(symbol);
      if (entry) {
        entry.data = data;
        entry.lastFetched = now;
        entry.isStale = false;

        // Notify all subscribers
        this.notifySubscribers(symbol, data);
      }
    });
  }

  /**
   * Mark symbols as stale (need refresh)
   */
  markStale(symbols: string[]): void {
    symbols.forEach((symbol) => {
      const entry = this.cache.get(symbol);
      if (entry) {
        entry.isStale = true;
      }
    });
  }

  /**
   * Get symbols that need updating
   */
  getStaleSymbols(): string[] {
    const now = Date.now();
    const staleSymbols: string[] = [];

    this.cache.forEach((entry, symbol) => {
      if (
        entry.subscribers.size > 0 &&
        (entry.isStale || now - entry.lastFetched > this.STALE_THRESHOLD)
      ) {
        staleSymbols.push(symbol);
      }
    });

    return staleSymbols;
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    totalSymbols: number;
    totalSubscribers: number;
    staleSymbols: number;
    cacheHitRate: number;
  } {
    let totalSubscribers = 0;
    let staleSymbols = 0;
    const now = Date.now();

    this.cache.forEach((entry) => {
      totalSubscribers += entry.subscribers.size;
      if (entry.isStale || now - entry.lastFetched > this.STALE_THRESHOLD) {
        staleSymbols++;
      }
    });

    return {
      totalSymbols: this.cache.size,
      totalSubscribers,
      staleSymbols,
      cacheHitRate:
        this.cache.size > 0
          ? ((this.cache.size - staleSymbols) / this.cache.size) * 100
          : 0,
    };
  }

  /**
   * Notify subscribers of symbol updates
   */
  private notifySubscribers(symbol: string, data: PriceData): void {
    const callbacks = this.subscriptions.get(symbol);
    if (callbacks) {
      callbacks.forEach((callback) => {
        try {
          callback(symbol, data);
        } catch (error) {
          console.error(
            `Error in symbol subscription callback for ${symbol}:`,
            error
          );
        }
      });
    }
  }

  /**
   * Start cleanup process
   */
  private startCleanup(): void {
    // Clean up every 10 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 10 * 60 * 1000);
  }

  /**
   * Cleanup stale entries and inactive subscribers
   */
  private cleanup(): void {
    const now = Date.now();
    const entriesToDelete: string[] = [];

    this.cache.forEach((entry, symbol) => {
      // Remove entries with no subscribers
      if (entry.subscribers.size === 0) {
        entriesToDelete.push(symbol);
      }
      // Remove very old entries (1 hour)
      else if (now - entry.lastFetched > 60 * 60 * 1000) {
        entriesToDelete.push(symbol);
      }
    });

    entriesToDelete.forEach((symbol) => {
      this.cache.delete(symbol);
      this.subscriptions.delete(symbol);
    });

    if (entriesToDelete.length > 0) {
      console.log(
        `Global cache cleanup: removed ${entriesToDelete.length} stale entries`
      );
    }
  }

  /**
   * Destroy the cache service
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.cache.clear();
    this.subscriptions.clear();
  }
}

// Singleton instance
let globalCacheInstance: GlobalSymbolCacheService | null = null;

export function getGlobalSymbolCache(): GlobalSymbolCacheService {
  if (!globalCacheInstance) {
    globalCacheInstance = new GlobalSymbolCacheService();
  }
  return globalCacheInstance;
}

export function destroyGlobalSymbolCache(): void {
  if (globalCacheInstance) {
    globalCacheInstance.destroy();
    globalCacheInstance = null;
  }
}

export type { CacheEntry, SubscriptionCallback };
