/**
 * Market Aware Updater Service
 * Handles market hours detection and update frequency management
 * Phase 1: Core Infrastructure
 */

import { toZonedTime } from "date-fns-tz";

// US Market timezone (Eastern Time)
const MARKET_TIMEZONE = "America/New_York";

// US Market holidays for 2024-2025 (expandable)
const US_MARKET_HOLIDAYS = [
  // 2024
  "2024-01-01",
  "2024-01-15",
  "2024-02-19",
  "2024-03-29",
  "2024-05-27",
  "2024-06-19",
  "2024-07-04",
  "2024-09-02",
  "2024-11-28",
  "2024-12-25",
  // 2025
  "2025-01-01",
  "2025-01-20",
  "2025-02-17",
  "2025-04-18",
  "2025-05-26",
  "2025-06-19",
  "2025-07-04",
  "2025-09-01",
  "2025-11-27",
  "2025-12-25",
];

interface MarketStatus {
  isOpen: boolean;
  isHoliday: boolean;
  isWeekend: boolean;
  currentTime: Date;
  marketTime: Date;
  nextOpen: Date | null;
  nextClose: Date | null;
}

interface UpdateSchedule {
  shouldUpdate: boolean;
  updateFrequency: number; // milliseconds
  reason: string;
}

class MarketAwareUpdater {
  private readonly MARKET_OPEN_HOUR = 9;
  private readonly MARKET_OPEN_MINUTE = 30;
  private readonly MARKET_CLOSE_HOUR = 16;
  private readonly MARKET_CLOSE_MINUTE = 0;

  // Extended hours for auto-refresh (30 minutes before and after market hours)
  private readonly EXTENDED_OPEN_HOUR = 9;
  private readonly EXTENDED_OPEN_MINUTE = 0; // 9:00 AM ET (30 min before market)
  private readonly EXTENDED_CLOSE_HOUR = 16;
  private readonly EXTENDED_CLOSE_MINUTE = 30; // 4:30 PM ET (30 min after market)

  // Update frequencies in milliseconds
  private readonly UPDATE_FREQUENCIES = {
    MARKET_HOURS: 2 * 60 * 1000, // 2 minutes during market hours
    AFTER_HOURS: 0, // No updates after hours
    WEEKENDS: 0, // No updates on weekends
    HOLIDAYS: 0, // No updates on holidays
  };

  /**
   * Get current market status
   */
  getMarketStatus(): MarketStatus {
    const now = new Date();
    const marketTime = toZonedTime(now, MARKET_TIMEZONE);

    const day = marketTime.getDay(); // 0 = Sunday, 6 = Saturday
    const hours = marketTime.getHours();
    const minutes = marketTime.getMinutes();
    const dateString = marketTime.toISOString().split("T")[0];

    // Check if weekend
    const isWeekend = day === 0 || day === 6;

    // Check if holiday
    const isHoliday = US_MARKET_HOLIDAYS.includes(dateString);

    // Check if within trading hours (9:30 AM - 4:00 PM ET)
    const isWithinHours =
      (hours > this.MARKET_OPEN_HOUR ||
        (hours === this.MARKET_OPEN_HOUR &&
          minutes >= this.MARKET_OPEN_MINUTE)) &&
      (hours < this.MARKET_CLOSE_HOUR ||
        (hours === this.MARKET_CLOSE_HOUR &&
          minutes < this.MARKET_CLOSE_MINUTE));

    const isOpen = !isWeekend && !isHoliday && isWithinHours;

    return {
      isOpen,
      isHoliday,
      isWeekend,
      currentTime: now,
      marketTime,
      nextOpen: this.getNextMarketOpen(marketTime),
      nextClose: isOpen ? this.getNextMarketClose(marketTime) : null,
    };
  }

  /**
   * Check if we're in extended trading hours (9:00 AM - 4:30 PM ET)
   * This includes 30 minutes before market open and 30 minutes after market close
   */
  isInExtendedHours(): boolean {
    const now = new Date();
    const marketTime = toZonedTime(now, MARKET_TIMEZONE);

    const day = marketTime.getDay(); // 0 = Sunday, 6 = Saturday
    const hours = marketTime.getHours();
    const minutes = marketTime.getMinutes();
    const dateString = marketTime.toISOString().split("T")[0];

    // Check if weekend
    const isWeekend = day === 0 || day === 6;

    // Check if holiday
    const isHoliday = US_MARKET_HOLIDAYS.includes(dateString);

    // Check if within extended hours (9:00 AM - 4:30 PM ET)
    const isWithinExtendedHours =
      (hours > this.EXTENDED_OPEN_HOUR ||
        (hours === this.EXTENDED_OPEN_HOUR &&
          minutes >= this.EXTENDED_OPEN_MINUTE)) &&
      (hours < this.EXTENDED_CLOSE_HOUR ||
        (hours === this.EXTENDED_CLOSE_HOUR &&
          minutes < this.EXTENDED_CLOSE_MINUTE));

    return !isWeekend && !isHoliday && isWithinExtendedHours;
  }

  /**
   * Determine if updates should occur and at what frequency
   */
  getUpdateSchedule(): UpdateSchedule {
    const marketStatus = this.getMarketStatus();
    const inExtendedHours = this.isInExtendedHours();

    if (marketStatus.isWeekend) {
      return {
        shouldUpdate: false,
        updateFrequency: this.UPDATE_FREQUENCIES.WEEKENDS,
        reason: "Weekend - no updates",
      };
    }

    if (marketStatus.isHoliday) {
      return {
        shouldUpdate: false,
        updateFrequency: this.UPDATE_FREQUENCIES.HOLIDAYS,
        reason: "Market holiday - no updates",
      };
    }

    if (inExtendedHours) {
      if (marketStatus.isOpen) {
        return {
          shouldUpdate: true,
          updateFrequency: this.UPDATE_FREQUENCIES.MARKET_HOURS,
          reason: "Market open - 2 minute updates",
        };
      } else {
        return {
          shouldUpdate: true,
          updateFrequency: this.UPDATE_FREQUENCIES.MARKET_HOURS,
          reason: "Extended hours (pre/post market) - 2 minute updates",
        };
      }
    }

    // After extended hours on weekdays
    return {
      shouldUpdate: false,
      updateFrequency: this.UPDATE_FREQUENCIES.AFTER_HOURS,
      reason: "After extended hours - no updates",
    };
  }

  /**
   * Check if updates should be running right now
   */
  shouldUpdate(): boolean {
    return this.getUpdateSchedule().shouldUpdate;
  }

  /**
   * Check if updates should be running right now (extended hours version)
   * This is the main method that should be used for auto-refresh logic
   */
  shouldUpdateExtended(): boolean {
    return this.isInExtendedHours();
  }

  /**
   * Get the current update frequency in milliseconds
   */
  getUpdateFrequency(): number {
    return this.getUpdateSchedule().updateFrequency;
  }

  /**
   * Get the next market open time
   */
  private getNextMarketOpen(currentMarketTime: Date): Date | null {
    let nextOpen = new Date(currentMarketTime);

    // Set to 9:30 AM
    nextOpen.setHours(this.MARKET_OPEN_HOUR, this.MARKET_OPEN_MINUTE, 0, 0);

    // If it's already past 9:30 AM today, move to next day
    if (
      currentMarketTime.getHours() > this.MARKET_OPEN_HOUR ||
      (currentMarketTime.getHours() === this.MARKET_OPEN_HOUR &&
        currentMarketTime.getMinutes() >= this.MARKET_OPEN_MINUTE)
    ) {
      nextOpen.setDate(nextOpen.getDate() + 1);
    }

    // Find next valid trading day
    let attempts = 0;
    while (attempts < 10) {
      // Safety limit
      const day = nextOpen.getDay();
      const dateString = nextOpen.toISOString().split("T")[0];

      // If it's a weekday and not a holiday
      if (day !== 0 && day !== 6 && !US_MARKET_HOLIDAYS.includes(dateString)) {
        return nextOpen;
      }

      // Move to next day
      nextOpen.setDate(nextOpen.getDate() + 1);
      attempts++;
    }

    return null; // Couldn't find next open day
  }

  /**
   * Get the next market close time (only if market is currently open)
   */
  private getNextMarketClose(currentMarketTime: Date): Date | null {
    const nextClose = new Date(currentMarketTime);
    nextClose.setHours(this.MARKET_CLOSE_HOUR, this.MARKET_CLOSE_MINUTE, 0, 0);

    // If it's already past 4:00 PM, this shouldn't be called
    if (currentMarketTime.getHours() >= this.MARKET_CLOSE_HOUR) {
      return null;
    }

    return nextClose;
  }

  /**
   * Get time until next market event (open or close)
   */
  getTimeUntilNextEvent(): { event: string; timeMs: number } | null {
    const marketStatus = this.getMarketStatus();

    if (marketStatus.isOpen && marketStatus.nextClose) {
      return {
        event: "market_close",
        timeMs:
          marketStatus.nextClose.getTime() - marketStatus.currentTime.getTime(),
      };
    }

    if (!marketStatus.isOpen && marketStatus.nextOpen) {
      return {
        event: "market_open",
        timeMs:
          marketStatus.nextOpen.getTime() - marketStatus.currentTime.getTime(),
      };
    }

    return null;
  }

  /**
   * Get human-readable status
   */
  getStatusMessage(): string {
    const schedule = this.getUpdateSchedule();
    const marketStatus = this.getMarketStatus();
    const inExtendedHours = this.isInExtendedHours();

    if (marketStatus.isOpen) {
      return `Market open - Updates every ${
        schedule.updateFrequency / 60000
      } minutes`;
    }

    if (inExtendedHours && !marketStatus.isOpen) {
      return `Extended hours (pre/post market) - Updates every ${
        schedule.updateFrequency / 60000
      } minutes`;
    }

    if (marketStatus.isWeekend) {
      return "Weekend - No updates until Monday 9:00 AM ET";
    }

    if (marketStatus.isHoliday) {
      return "Market holiday - No updates today";
    }

    const nextEvent = this.getTimeUntilNextEvent();
    if (nextEvent && nextEvent.event === "market_open") {
      const hoursUntilOpen = Math.floor(nextEvent.timeMs / (1000 * 60 * 60));
      const minutesUntilOpen = Math.floor(
        (nextEvent.timeMs % (1000 * 60 * 60)) / (1000 * 60)
      );
      return `After extended hours - Updates resume in ${hoursUntilOpen}h ${minutesUntilOpen}m`;
    }

    return "After extended hours - No updates until 9:00 AM ET";
  }

  /**
   * Calculate staggered update times for batches
   * Returns array of delay times in milliseconds for each batch
   */
  calculateStaggeredDelays(batchCount: number): number[] {
    if (batchCount <= 0) return [];

    const updateFrequency = this.getUpdateFrequency();
    if (updateFrequency === 0) return new Array(batchCount).fill(0);

    // Distribute batches evenly across the update interval
    const delayBetweenBatches = Math.floor(updateFrequency / batchCount);

    return Array.from(
      { length: batchCount },
      (_, index) => index * delayBetweenBatches
    );
  }
}

// Singleton instance
let marketAwareUpdaterInstance: MarketAwareUpdater | null = null;

/**
 * Get the market aware updater instance
 */
export function getMarketAwareUpdater(): MarketAwareUpdater {
  if (!marketAwareUpdaterInstance) {
    marketAwareUpdaterInstance = new MarketAwareUpdater();
  }
  return marketAwareUpdaterInstance;
}

/**
 * Reset the market aware updater (for testing)
 */
export function resetMarketAwareUpdater(): void {
  marketAwareUpdaterInstance = null;
}
