/**
 * Tests for Global Symbol Cache Service
 */

import { getGlobalSymbolCache, destroyGlobalSymbolCache } from '../global-symbol-cache';
import { PriceData } from '@/types/price';

// Mock PriceData
const mockPriceData: Record<string, PriceData> = {
  AAPL: {
    price: 150.25,
    previousClose: 148.50,
    change: 1.75,
    changePercent: 1.18,
    lastUpdated: new Date().toISOString(),
    name: 'Apple Inc.'
  },
  TSLA: {
    price: 245.80,
    previousClose: 248.30,
    change: -2.50,
    changePercent: -1.01,
    lastUpdated: new Date().toISOString(),
    name: 'Tesla Inc.'
  }
};

describe('GlobalSymbolCache', () => {
  beforeEach(() => {
    // Clean up before each test
    destroyGlobalSymbolCache();
  });

  afterEach(() => {
    // Clean up after each test
    destroyGlobalSymbolCache();
  });

  test('should subscribe and unsubscribe symbols', () => {
    const cache = getGlobalSymbolCache();
    const sessionId = 'test-session-1';
    const symbols = ['AAPL', 'TSLA'];
    const callback = jest.fn();

    // Subscribe
    cache.subscribe(sessionId, symbols, callback);
    
    const stats = cache.getStats();
    expect(stats.totalSymbols).toBe(2);
    expect(stats.totalSubscribers).toBe(2); // One subscriber per symbol

    // Unsubscribe
    cache.unsubscribe(sessionId, symbols, callback);
    
    const statsAfter = cache.getStats();
    expect(statsAfter.totalSymbols).toBe(0);
    expect(statsAfter.totalSubscribers).toBe(0);
  });

  test('should return cached data and missing symbols', () => {
    const cache = getGlobalSymbolCache();
    const sessionId = 'test-session-1';
    
    // Subscribe to symbols
    cache.subscribe(sessionId, ['AAPL', 'TSLA', 'MSFT'], jest.fn());
    
    // Update cache with some data
    cache.updateCache({
      AAPL: mockPriceData.AAPL,
      TSLA: mockPriceData.TSLA
    });

    // Request data
    const result = cache.getCachedData(['AAPL', 'TSLA', 'MSFT']);
    
    expect(Object.keys(result.cached)).toHaveLength(2);
    expect(result.cached.AAPL).toEqual(mockPriceData.AAPL);
    expect(result.cached.TSLA).toEqual(mockPriceData.TSLA);
    expect(result.missing).toEqual(['MSFT']);
  });

  test('should notify subscribers when cache is updated', () => {
    const cache = getGlobalSymbolCache();
    const sessionId = 'test-session-1';
    const callback = jest.fn();

    // Subscribe
    cache.subscribe(sessionId, ['AAPL'], callback);

    // Update cache
    cache.updateCache({ AAPL: mockPriceData.AAPL });

    // Check if callback was called
    expect(callback).toHaveBeenCalledWith('AAPL', mockPriceData.AAPL);
  });

  test('should identify stale symbols', () => {
    const cache = getGlobalSymbolCache();
    const sessionId = 'test-session-1';

    // Subscribe to symbols
    cache.subscribe(sessionId, ['AAPL', 'TSLA'], jest.fn());

    // Mark symbols as stale
    cache.markStale(['AAPL']);

    const staleSymbols = cache.getStaleSymbols();
    expect(staleSymbols).toContain('AAPL');
    expect(staleSymbols).toContain('TSLA'); // TSLA is also stale (never updated)
  });

  test('should handle multiple subscribers for same symbol', () => {
    const cache = getGlobalSymbolCache();
    const callback1 = jest.fn();
    const callback2 = jest.fn();

    // Two different sessions subscribe to same symbol
    cache.subscribe('session-1', ['AAPL'], callback1);
    cache.subscribe('session-2', ['AAPL'], callback2);

    const stats = cache.getStats();
    expect(stats.totalSymbols).toBe(1);
    expect(stats.totalSubscribers).toBe(2);

    // Update cache
    cache.updateCache({ AAPL: mockPriceData.AAPL });

    // Both callbacks should be called
    expect(callback1).toHaveBeenCalledWith('AAPL', mockPriceData.AAPL);
    expect(callback2).toHaveBeenCalledWith('AAPL', mockPriceData.AAPL);
  });

  test('should clean up symbols with no subscribers', () => {
    const cache = getGlobalSymbolCache();
    const sessionId = 'test-session-1';
    const callback = jest.fn();

    // Subscribe
    cache.subscribe(sessionId, ['AAPL'], callback);
    expect(cache.getStats().totalSymbols).toBe(1);

    // Unsubscribe
    cache.unsubscribe(sessionId, ['AAPL'], callback);
    expect(cache.getStats().totalSymbols).toBe(0);
  });
});
