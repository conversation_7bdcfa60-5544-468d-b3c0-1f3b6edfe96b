import { createServerSupabaseClient } from "@/lib/supabase/server";

/**
 * Get prices for multiple symbols from Supabase
 * @param symbols Array of stock symbols
 * @returns Object mapping symbols to price data
 */
export async function getPricesFromSupabase(symbols: string[]) {
  if (!symbols || symbols.length === 0) {
    return {};
  }

  try {
    // Initialize Supabase client
    const supabase = await createServerSupabaseClient();

    // Call the database function to get prices for the symbols
    const { data, error } = await supabase.rpc("get_prices_for_symbols", {
      p_symbols: symbols,
    });

    if (error) {
      console.error("Error fetching prices from Supabase:", error);
      return {};
    }

    // Format the response
    const prices: Record<string, any> = {};

    // Convert array of price objects to a map keyed by symbol
    data?.forEach((price) => {
      prices[price.symbol] = {
        price: price.current_price,
        previousClose: price.previous_close,
        change: price.change_amount,
        changePercent: price.change_percent,
        lastUpdated: price.last_updated,
        name: price.name, // Include the name from the database
      };
    });

    return prices;
  } catch (error) {
    console.error("Error processing price request:", error);
    return {};
  }
}

/**
 * Search for symbols in Supabase
 * @param query Search query
 * @param limit Maximum number of results to return
 * @returns Array of symbol objects
 */
export async function searchSymbols(query: string, limit: number = 10) {
  if (!query || query.length < 1) {
    return [];
  }

  try {
    const supabase = await createServerSupabaseClient();

    // Search for symbols that match the query
    const { data, error } = await supabase
      .from("stock_symbols")
      .select("symbol, name, type, exchange, popularity_rank")
      .or(`symbol.ilike.%${query}%,name.ilike.%${query}%`)
      .eq("is_active", true)
      .order("popularity_rank", { ascending: true })
      .limit(limit);

    if (error) {
      console.error("Error searching for symbols:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error searching for symbols:", error);
    return [];
  }
}
