import NodeCache from 'node-cache';
import { getCacheTTL } from '../utils/market-hours';

// Create a cache instance
const priceCache = new NodeCache({
  stdTTL: 60, // Default TTL: 60 seconds
  checkperiod: 30, // Check for expired keys every 30 seconds
});

interface CachedPrice {
  price: number;
  timestamp: number;
}

/**
 * Get a stock price from the cache
 * @param symbol The stock symbol to look up
 * @returns The cached price or null if not found
 */
export function getCachedPrice(symbol: string): number | null {
  const normalizedSymbol = symbol.toUpperCase().trim();
  const cachedData = priceCache.get<CachedPrice>(normalizedSymbol);
  
  if (!cachedData) {
    return null;
  }
  
  return cachedData.price;
}

/**
 * Store a stock price in the cache
 * @param symbol The stock symbol
 * @param price The current price
 * @param customTTL Optional custom TTL in seconds
 */
export function cachePrice(symbol: string, price: number, customTTL?: number): void {
  const normalizedSymbol = symbol.toUpperCase().trim();
  const ttl = customTTL || getCacheTTL();
  
  const cacheData: CachedPrice = {
    price,
    timestamp: Date.now(),
  };
  
  priceCache.set(normalizedSymbol, cacheData, ttl);
}

/**
 * Get all cached symbols and their prices
 * @returns An object mapping symbols to prices
 */
export function getAllCachedPrices(): Record<string, number> {
  const keys = priceCache.keys();
  const result: Record<string, number> = {};
  
  for (const key of keys) {
    const cachedData = priceCache.get<CachedPrice>(key);
    if (cachedData) {
      result[key] = cachedData.price;
    }
  }
  
  return result;
}

/**
 * Clear the entire price cache
 */
export function clearPriceCache(): void {
  priceCache.flushAll();
}

/**
 * Remove a specific symbol from the cache
 * @param symbol The stock symbol to remove
 */
export function removeCachedPrice(symbol: string): void {
  const normalizedSymbol = symbol.toUpperCase().trim();
  priceCache.del(normalizedSymbol);
}

/**
 * Get cache statistics
 * @returns Object with cache stats
 */
export function getCacheStats() {
  return {
    keys: priceCache.keys().length,
    hits: priceCache.getStats().hits,
    misses: priceCache.getStats().misses,
  };
}
