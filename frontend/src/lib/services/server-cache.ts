/**
 * Server-Only Cache Service
 * This runs only on the server side and provides caching for API routes
 */

interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

interface CacheStats {
  totalEntries: number;
  hitRate: number;
  totalHits: number;
  totalMisses: number;
}

class ServerCache {
  private cache = new Map<string, CacheEntry>();
  private stats = {
    hits: 0,
    misses: 0,
  };

  /**
   * Get an item from cache
   */
  get(key: string): any | null {
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    this.stats.hits++;
    return entry.data;
  }

  /**
   * Set an item in cache
   */
  set(key: string, data: any, ttl: number = 300000): void {
    // Default 5 minutes
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Delete an item from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
    this.stats.hits = 0;
    this.stats.misses = 0;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    return {
      totalEntries: this.cache.size,
      hitRate: total > 0 ? (this.stats.hits / total) * 100 : 0,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
    };
  }

  /**
   * Clean up expired entries
   */
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    return cleaned;
  }

  /**
   * Get multiple items from cache
   */
  getMultiple(keys: string[]): {
    cached: Record<string, any>;
    missing: string[];
  } {
    const cached: Record<string, any> = {};
    const missing: string[] = [];

    for (const key of keys) {
      const value = this.get(key);
      if (value !== null) {
        cached[key] = value;
      } else {
        missing.push(key);
      }
    }

    return { cached, missing };
  }

  /**
   * Set multiple items in cache
   */
  setMultiple(items: Record<string, any>, ttl: number = 300000): void {
    for (const [key, value] of Object.entries(items)) {
      this.set(key, value, ttl);
    }
  }
}

// Singleton instance
let serverCacheInstance: ServerCache | null = null;

/**
 * Get the server cache instance
 * Only works on server-side
 */
export function getServerCache(): ServerCache {
  // Only create cache on server-side
  if (typeof window !== "undefined") {
    throw new Error("Server cache can only be used on the server side");
  }

  if (!serverCacheInstance) {
    serverCacheInstance = new ServerCache();

    // Set up periodic cleanup (every 10 minutes)
    setInterval(() => {
      const cleaned = serverCacheInstance!.cleanup();
      if (cleaned > 0) {
        console.log(`Server cache cleaned up ${cleaned} expired entries`);
      }
    }, 10 * 60 * 1000);
  }

  return serverCacheInstance;
}

/**
 * Helper function to generate cache keys for price data
 */
export function generatePriceCacheKey(symbol: string): string {
  return `price:${symbol.toUpperCase()}`;
}

/**
 * Helper function to generate cache keys for symbol batches
 */
export function generateBatchCacheKey(symbols: string[]): string {
  const sortedSymbols = symbols.map((s) => s.toUpperCase()).sort();
  return `batch:${sortedSymbols.join(",")}`;
}

/**
 * Enhanced Server Cache with batch operations
 * Phase 1: Core Infrastructure Enhancement
 */
interface BatchStats {
  totalBatches: number;
  batchHitRate: number;
  lastBatchUpdate: Date | null;
  batchSizes: number[];
}

interface PopularSymbolStats {
  symbol: string;
  requestCount: number;
  lastRequested: Date;
  cacheHits: number;
  cacheMisses: number;
}

class EnhancedServerCache extends ServerCache {
  private batchStats: Map<
    string,
    { hits: number; misses: number; lastUpdate: Date }
  > = new Map();
  private popularSymbols: Map<string, PopularSymbolStats> = new Map();

  /**
   * Set multiple items in cache with batch tracking
   */
  setBatch(
    symbols: string[],
    data: Record<string, any>,
    ttl: number = 300000
  ): void {
    const batchKey = generateBatchCacheKey(symbols);
    const now = new Date();

    // Set individual symbols
    for (const symbol of symbols) {
      if (data[symbol]) {
        this.set(generatePriceCacheKey(symbol), data[symbol], ttl);
      }
    }

    // Track batch statistics
    if (!this.batchStats.has(batchKey)) {
      this.batchStats.set(batchKey, { hits: 0, misses: 0, lastUpdate: now });
    }

    const batchStat = this.batchStats.get(batchKey)!;
    batchStat.lastUpdate = now;

    console.log(
      `Batch cache updated: ${
        symbols.length
      } symbols, key: ${batchKey.substring(0, 50)}...`
    );
  }

  /**
   * Get batch statistics
   */
  getBatchStats(): BatchStats {
    const batches = Array.from(this.batchStats.values());
    const totalHits = batches.reduce((sum, batch) => sum + batch.hits, 0);
    const totalMisses = batches.reduce((sum, batch) => sum + batch.misses, 0);
    const total = totalHits + totalMisses;

    return {
      totalBatches: this.batchStats.size,
      batchHitRate: total > 0 ? (totalHits / total) * 100 : 0,
      lastBatchUpdate:
        batches.length > 0
          ? new Date(Math.max(...batches.map((b) => b.lastUpdate.getTime())))
          : null,
      batchSizes: [], // Will be populated by batch updater
    };
  }

  /**
   * Track symbol popularity
   */
  trackSymbolRequest(symbol: string, wasHit: boolean): void {
    const upperSymbol = symbol.toUpperCase();

    if (!this.popularSymbols.has(upperSymbol)) {
      this.popularSymbols.set(upperSymbol, {
        symbol: upperSymbol,
        requestCount: 0,
        lastRequested: new Date(),
        cacheHits: 0,
        cacheMisses: 0,
      });
    }

    const stats = this.popularSymbols.get(upperSymbol)!;
    stats.requestCount++;
    stats.lastRequested = new Date();

    if (wasHit) {
      stats.cacheHits++;
    } else {
      stats.cacheMisses++;
    }
  }

  /**
   * Get most popular symbols
   */
  getPopularSymbols(limit: number = 50): PopularSymbolStats[] {
    return Array.from(this.popularSymbols.values())
      .sort((a, b) => b.requestCount - a.requestCount)
      .slice(0, limit);
  }

  /**
   * Mark symbol as popular (for dynamic promotion)
   */
  markSymbolAsPopular(symbol: string): void {
    this.trackSymbolRequest(symbol, false); // Initialize if not exists
  }

  /**
   * Get enhanced cache statistics
   */
  getEnhancedStats() {
    const baseStats = this.getStats();
    const batchStats = this.getBatchStats();
    const popularSymbols = this.getPopularSymbols(10);

    return {
      ...baseStats,
      batch: batchStats,
      popular: popularSymbols,
      totalSymbolsTracked: this.popularSymbols.size,
    };
  }

  /**
   * Override get method to track symbol requests
   */
  get(key: string): any | null {
    const result = super.get(key);

    // Extract symbol from cache key if it's a price key
    if (key.startsWith("price:")) {
      const symbol = key.replace("price:", "");
      this.trackSymbolRequest(symbol, result !== null);
    }

    return result;
  }

  /**
   * Clean up old popularity data
   */
  cleanupPopularityData(maxAge: number = 7 * 24 * 60 * 60 * 1000): number {
    // 7 days default
    const now = Date.now();
    let cleaned = 0;

    for (const [symbol, stats] of this.popularSymbols.entries()) {
      if (now - stats.lastRequested.getTime() > maxAge) {
        this.popularSymbols.delete(symbol);
        cleaned++;
      }
    }

    return cleaned;
  }
}

// Update the singleton to use enhanced cache
let enhancedServerCacheInstance: EnhancedServerCache | null = null;

/**
 * Get the enhanced server cache instance
 */
export function getEnhancedServerCache(): EnhancedServerCache {
  if (typeof window !== "undefined") {
    throw new Error(
      "Enhanced server cache can only be used on the server side"
    );
  }

  if (!enhancedServerCacheInstance) {
    enhancedServerCacheInstance = new EnhancedServerCache();

    // Set up periodic cleanup (every 10 minutes)
    setInterval(() => {
      const cleaned = enhancedServerCacheInstance!.cleanup();
      const popularityCleaned =
        enhancedServerCacheInstance!.cleanupPopularityData();

      if (cleaned > 0 || popularityCleaned > 0) {
        console.log(
          `Enhanced cache cleanup: ${cleaned} expired entries, ${popularityCleaned} old popularity records`
        );
      }
    }, 10 * 60 * 1000);
  }

  return enhancedServerCacheInstance;
}
