/**
 * Position Types
 * These types match the database schema for positions and position tags
 */

/**
 * Position represents an options trading position
 */
export interface Position {
  id: string;
  user_id: string;
  symbol: string;
  strategy_type: string;
  open_date: string; // ISO date string
  close_date: string | null;
  expiry_date: string; // ISO date string
  strike_prices: number[];
  credit_debit_amount: number;
  stock_price_at_open: number | null;
  vix_value: number | null;
  num_contracts: number;
  commission_paid: number | null;
  notes: string | null;
  status: "open" | "closed";
  closing_price: number | null;
  profit_loss: number | null;
  created_at: string;
  updated_at: string;
  tags?: string[] | PositionTag[]; // Can be either string array or PositionTag objects depending on context
}

/**
 * PositionTag represents a tag associated with a position
 */
export interface PositionTag {
  id: string;
  position_id: string;
  tag_name: string;
  created_at: string;
}

/**
 * NewPosition represents the data required to create a new position
 * Omits auto-generated fields
 */
export type NewPosition = Omit<
  Position,
  "id" | "user_id" | "created_at" | "updated_at" | "tags"
> & {
  tags?: string[]; // Just the tag names for creation
};

/**
 * UpdatePosition represents the data that can be updated for a position
 * Makes most fields optional except id
 */
export type UpdatePosition = Partial<
  Omit<Position, "id" | "user_id" | "created_at" | "updated_at" | "tags">
> & {
  id: string;
  tags?: string[]; // Just the tag names for updating
};

/**
 * PositionWithTags extends Position to include tags
 */
export interface PositionWithTags extends Omit<Position, "tags"> {
  tags: PositionTag[];
}

/**
 * PositionFilters defines the available filters for positions
 */
export interface PositionFilters {
  status?: "open" | "closed" | "all";
  symbol?: string;
  strategy_type?: string;
  tags?: string[];
  dateRange?: {
    start?: string;
    end?: string;
  };
}

/**
 * PositionSort defines sorting options for positions
 */
export interface PositionSort {
  field: keyof Position;
  direction: "asc" | "desc";
}
