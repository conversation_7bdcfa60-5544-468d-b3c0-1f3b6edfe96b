/**
 * This is a placeholder type for the Supabase database schema.
 * Ideally, this would be auto-generated from the Supabase API.
 */
export type Database = {
  public: {
    Tables: {
      positions: {
        Row: {
          id: string;
          user_id: string;
          symbol: string;
          strategy_type: string;
          open_date: string;
          close_date: string | null;
          expiry_date: string;
          strike_prices: number[];
          credit_debit_amount: number;
          stock_price_at_open: number | null;
          vix_value: number | null;
          num_contracts: number;
          commission_paid: number | null;
          notes: string | null;
          status: "open" | "closed";
          profit_loss: number | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          symbol: string;
          strategy_type: string;
          open_date: string;
          close_date?: string | null;
          expiry_date: string;
          strike_prices: number[];
          credit_debit_amount: number;
          stock_price_at_open?: number | null;
          vix_value?: number | null;
          num_contracts?: number;
          commission_paid?: number | null;
          notes?: string | null;
          status?: "open" | "closed";
          profit_loss?: number | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          symbol?: string;
          strategy_type?: string;
          open_date?: string;
          close_date?: string | null;
          expiry_date?: string;
          strike_prices?: number[];
          credit_debit_amount?: number;
          stock_price_at_open?: number | null;
          vix_value?: number | null;
          num_contracts?: number;
          commission_paid?: number | null;
          notes?: string | null;
          status?: "open" | "closed";
          profit_loss?: number | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      tags: {
        Row: {
          id: number;
          name: string;
          user_id: string;
          created_at: string;
        };
        Insert: {
          id?: number;
          name: string;
          user_id: string;
          created_at?: string;
        };
        Update: {
          id?: number;
          name?: string;
          user_id?: string;
          created_at?: string;
        };
      };
      position_tags: {
        Row: {
          id: string;
          position_id: string;
          tag_id: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          position_id: string;
          tag_id: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          position_id?: string;
          tag_id?: number;
          created_at?: string;
        };
      };
      profiles: {
        Row: {
          id: string;
          full_name: string | null;
          avatar_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          full_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      users: {
        Row: {
          id: string;
          email: string;
          profile_data: Record<string, unknown> | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          profile_data?: Record<string, unknown> | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          profile_data?: Record<string, unknown> | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      stock_symbols: {
        Row: {
          symbol: string;
          name: string;
          type: string;
          exchange: string;
          is_active: boolean;
          popularity_rank: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          symbol: string;
          name: string;
          type: string;
          exchange: string;
          is_active?: boolean;
          popularity_rank: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          symbol?: string;
          name?: string;
          type?: string;
          exchange?: string;
          is_active?: boolean;
          popularity_rank?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      stock_prices: {
        Row: {
          id: number;
          symbol: string;
          current_price: number;
          previous_close: number;
          open_price: number;
          day_high: number;
          day_low: number;
          change_amount: number;
          change_percent: number;
          volume: number;
          market_cap: number;
          last_updated: string;
          fifty_two_week_high: number | null;
          fifty_two_week_low: number | null;
          pe_ratio: number | null;
          dividend_yield: number | null;
        };
        Insert: {
          id?: number;
          symbol: string;
          current_price: number;
          previous_close: number;
          open_price: number;
          day_high: number;
          day_low: number;
          change_amount: number;
          change_percent: number;
          volume: number;
          market_cap: number;
          last_updated?: string;
          fifty_two_week_high?: number | null;
          fifty_two_week_low?: number | null;
          pe_ratio?: number | null;
          dividend_yield?: number | null;
        };
        Update: {
          id?: number;
          symbol?: string;
          current_price?: number;
          previous_close?: number;
          open_price?: number;
          day_high?: number;
          day_low?: number;
          change_amount?: number;
          change_percent?: number;
          volume?: number;
          market_cap?: number;
          last_updated?: string;
          fifty_two_week_high?: number | null;
          fifty_two_week_low?: number | null;
          pe_ratio?: number | null;
          dividend_yield?: number | null;
        };
      };
    };
    Views: Record<string, never>;
    Functions: {
      get_prices_for_symbols: {
        Args: {
          p_symbols: string[];
        };
        Returns: {
          symbol: string;
          current_price: number;
          previous_close: number;
          change_amount: number;
          change_percent: number;
          last_updated: string;
        }[];
      };
      get_symbols_for_update: {
        Args: {
          p_limit?: number;
        };
        Returns: {
          symbol: string;
        }[];
      };
      get_symbols_without_prices: {
        Args: {
          p_limit?: number;
        };
        Returns: {
          symbol: string;
        }[];
      };
      upsert_stock_price: {
        Args: {
          p_symbol: string;
          p_current_price: number;
          p_previous_close: number;
          p_open_price: number;
          p_day_high: number;
          p_day_low: number;
          p_change_amount: number;
          p_change_percent: number;
          p_volume: number;
          p_market_cap: number;
          p_fifty_two_week_high?: number;
          p_fifty_two_week_low?: number;
          p_pe_ratio?: number;
          p_dividend_yield?: number;
        };
        Returns: void;
      };
      upsert_stock_symbol: {
        Args: {
          p_symbol: string;
          p_name: string;
          p_type: string;
          p_exchange: string;
          p_popularity_rank: number;
        };
        Returns: void;
      };
      update_symbol_active_status: {
        Args: {
          p_symbol: string;
          p_is_active: boolean;
        };
        Returns: void;
      };
    };
  };
};
