import { Suspense } from "react";
import Link from "next/link";
import {
  getAllPositions,
  updateExpiredPositions,
} from "@/lib/actions/positions";
import { PositionsTable } from "@/components/positions/positions-table";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { Position } from "@/types/position";

// Loading component for positions list
function PositionsLoading() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-white">
            Positions
          </h1>
          <p className="text-white/70">
            Manage and track your options positions
          </p>
        </div>
      </div>
      <div className="space-y-4">
        <div className="h-8 bg-muted animate-pulse rounded"></div>
        <div className="h-96 bg-muted animate-pulse rounded"></div>
      </div>
    </div>
  );
}

async function PositionsList() {
  // Update any expired positions to closed status
  await updateExpiredPositions();

  // Then fetch all positions (which will now have correct status)
  const positions: Position[] | null = await getAllPositions();

  if (!positions) {
    return (
      <div className="text-center p-8">
        <h3 className="text-xl font-semibold mb-2 text-white">
          Failed to load positions
        </h3>
        <p className="text-white/70">Please try again later</p>
      </div>
    );
  }

  return (
    <div>
      {positions.length === 0 ? (
        <div className="text-center p-12 border rounded-lg">
          <h3 className="text-xl font-semibold mb-2 text-white">
            No positions found
          </h3>
          <p className="text-white/70 mb-6">
            Get started by creating your first options position
          </p>
          <Button asChild>
            <Link href="/dashboard/positions/new">
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Position
            </Link>
          </Button>
        </div>
      ) : (
        <PositionsTable positions={positions} />
      )}
    </div>
  );
}

async function PositionsContent() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-white">
            Positions
          </h1>
          <p className="text-white/70">
            Manage and track your options positions
          </p>
        </div>
        <Button asChild>
          <Link href="/dashboard/positions/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Position
          </Link>
        </Button>
      </div>

      <Suspense fallback={<div>Loading positions...</div>}>
        <PositionsList />
      </Suspense>
    </div>
  );
}

export default function PositionsPage() {
  return (
    <Suspense fallback={<PositionsLoading />}>
      <PositionsContent />
    </Suspense>
  );
}
