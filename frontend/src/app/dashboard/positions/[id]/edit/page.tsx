import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { getPosition } from "@/lib/actions/positions";
import { PositionForm } from "@/components/positions/position-form";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface EditPositionPageProps {
  params: {
    id: string;
  };
}

// Loading component for edit position content
function EditPositionLoading() {
  return (
    <div className="flex flex-col gap-6">
      <div>
        <div className="h-10 w-32 bg-muted animate-pulse rounded mb-6"></div>
        <div className="h-8 w-56 bg-muted animate-pulse rounded mb-2"></div>
        <div className="h-4 w-64 bg-muted animate-pulse rounded"></div>
      </div>

      <div className="border border-[#3d2a50]/30 rounded-lg p-6 bg-[#1a1025]">
        <div className="space-y-6">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="h-16 bg-muted animate-pulse rounded"></div>
          ))}
        </div>
      </div>
    </div>
  );
}

async function EditPositionContent({ id }: { id: string }) {
  const position = await getPosition(id);

  if (!position) {
    return notFound();
  }

  return (
    <div className="border border-[#3d2a50]/30 rounded-lg p-6 bg-[#1a1025]">
      <PositionForm position={position} isEditing={true} />
    </div>
  );
}

async function EditPositionWrapper({ id }: { id: string }) {
  return (
    <div className="flex flex-col gap-6">
      <div>
        <Button variant="ghost" className="mb-6" asChild>
          <Link href={`/dashboard/positions/${id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Position
          </Link>
        </Button>

        <h1 className="text-3xl font-bold tracking-tight text-white">
          Edit Position
        </h1>
        <p className="text-white/70">
          Update the details of your options position.
        </p>
      </div>

      <Suspense fallback={<div>Loading position data...</div>}>
        <EditPositionContent id={id} />
      </Suspense>
    </div>
  );
}

export default async function EditPositionPage({
  params,
}: EditPositionPageProps) {
  // Properly await the params object to get the id
  const { id } = await params;

  return (
    <Suspense fallback={<EditPositionLoading />}>
      <EditPositionWrapper id={id} />
    </Suspense>
  );
}
