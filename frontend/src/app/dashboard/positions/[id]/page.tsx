import { getPosition, updateExpiredPositions } from "@/lib/actions/positions";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { PositionDetailsClient } from "@/components/positions/position-details-client";

interface PositionDetailsProps {
  params: {
    id: string;
  };
}

// Loading component for position details
function PositionDetailsLoading() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex justify-between items-start">
        <div>
          <div className="h-8 w-48 bg-muted animate-pulse rounded mb-2"></div>
          <div className="h-4 w-32 bg-muted animate-pulse rounded"></div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-[#1a1025] border-[#3d2a50]/30 rounded-lg border shadow-sm p-6">
          <div className="h-6 w-36 bg-muted animate-pulse rounded mb-4"></div>
          <div className="space-y-6">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="h-12 bg-muted animate-pulse rounded"
              ></div>
            ))}
          </div>
        </div>

        <div className="bg-[#1a1025] border-[#3d2a50]/30 rounded-lg border shadow-sm p-6">
          <div className="h-6 w-36 bg-muted animate-pulse rounded mb-4"></div>
          <div className="space-y-6">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="h-12 bg-muted animate-pulse rounded"
              ></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// This component fetches the position data
async function PositionDetailsContent({ id }: { id: string }) {
  // Update any expired positions to closed status
  await updateExpiredPositions();

  // Then fetch the position (which will now have correct status)
  const position = await getPosition(id);

  if (!position) {
    return notFound();
  }

  // Pass the position data to the client component
  return <PositionDetailsClient position={position} />;
}

export default async function PositionDetailsPage({
  params,
}: PositionDetailsProps) {
  // Properly await the params object to get the id
  const { id } = await params;

  return (
    <Suspense fallback={<PositionDetailsLoading />}>
      <PositionDetailsContent id={id} />
    </Suspense>
  );
}
