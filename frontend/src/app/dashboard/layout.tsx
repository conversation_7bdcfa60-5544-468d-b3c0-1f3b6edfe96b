import DashboardLayout from "@/components/layout/dashboard-layout";
import { createServerSupabaseClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";

export const metadata = {
  title: "Dashboard",
  description: "Manage your options trading dashboard",
};

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Ensure authentication
  const supabase = await createServerSupabaseClient();
  const { data } = await supabase.auth.getSession();

  if (!data.session) {
    redirect("/login");
  }

  return <DashboardLayout>{children}</DashboardLayout>;
}
