import LandingLayout from "@/components/layout/landing-layout";
import { AnimatedHero } from "@/components/landing/animated-hero";
import { AnimatedFeatures } from "@/components/landing/animated-features";
import { AnimatedExampleTrades } from "@/components/landing/animated-example-trades";

export default function LandingPage() {
  return (
    <LandingLayout>
      {/* Hero Section */}
      <section className="relative container mx-auto px-4 pt-32 text-white">
        <AnimatedHero />
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-24">
        <AnimatedFeatures />
      </section>

      {/* Example Trades Section */}
      <section className="container mx-auto px-4 py-16 pb-32">
        <AnimatedExampleTrades />
      </section>
    </LandingLayout>
  );
}
