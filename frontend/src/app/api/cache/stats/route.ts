import { NextResponse } from "next/server";
import { getGlobalSymbolCache } from "@/lib/services/global-symbol-cache";
import { getCacheUpdater } from "@/lib/services/cache-updater";

/**
 * GET /api/cache/stats
 * Returns global cache statistics for monitoring
 */
export async function GET() {
  try {
    const globalCache = getGlobalSymbolCache();
    const cacheUpdater = getCacheUpdater();

    const cacheStats = globalCache.getStats();
    const updaterStatus = cacheUpdater.getStatus();

    return NextResponse.json({
      cache: {
        totalSymbols: cacheStats.totalSymbols,
        totalSubscribers: cacheStats.totalSubscribers,
        staleSymbols: cacheStats.staleSymbols,
        cacheHitRate: `${cacheStats.cacheHitRate.toFixed(1)}%`,
        freshSymbols: cacheStats.totalSymbols - cacheStats.staleSymbols,
      },
      updater: {
        isRunning: updaterStatus.isRunning,
        isUpdating: updaterStatus.isUpdating,
        nextUpdate: updaterStatus.nextUpdate,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error getting cache stats:", error);
    return NextResponse.json(
      { error: "Failed to get cache statistics" },
      { status: 500 }
    );
  }
}
