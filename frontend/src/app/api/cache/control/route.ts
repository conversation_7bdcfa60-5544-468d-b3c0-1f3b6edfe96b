/**
 * Cache Control API
 * Provides manual control over batch cache updater
 * Phase 1: Core Infrastructure
 */

import { NextRequest, NextResponse } from "next/server";
import { getBatchCacheUpdater } from "@/lib/services/batch-cache-updater";
import { getSymbolManager } from "@/lib/services/symbol-manager";
import { getEnhancedServerCache } from "@/lib/services/server-cache";

/**
 * POST /api/cache/control
 * Control batch cache updater operations
 */
export async function POST(request: NextRequest) {
  try {
    const { action, batchIndex, symbol } = await request.json();
    const batchUpdater = getBatchCacheUpdater();
    const symbolManager = getSymbolManager();
    const cache = getEnhancedServerCache();

    switch (action) {
      case "start":
        batchUpdater.start();
        return NextResponse.json({
          success: true,
          message: "Batch cache updater started",
          timestamp: new Date().toISOString(),
        });

      case "stop":
        batchUpdater.stop();
        return NextResponse.json({
          success: true,
          message: "Batch cache updater stopped",
          timestamp: new Date().toISOString(),
        });

      case "update_batch":
        if (typeof batchIndex !== "number") {
          return NextResponse.json(
            { error: "batchIndex is required for update_batch action" },
            { status: 400 }
          );
        }

        const result = await batchUpdater.updateBatch(batchIndex);
        return NextResponse.json({
          success: result.success,
          message: result.success
            ? `Batch ${batchIndex} updated successfully`
            : `Batch ${batchIndex} update failed: ${result.error}`,
          result,
          timestamp: new Date().toISOString(),
        });

      case "update_all":
        const results = await batchUpdater.forceUpdateAllBatches();
        const successCount = results.filter((r) => r.success).length;

        return NextResponse.json({
          success: successCount === results.length,
          message: `Updated ${successCount}/${results.length} batches`,
          results,
          timestamp: new Date().toISOString(),
        });

      case "health_check":
        const health = batchUpdater.getHealthStatus();
        return NextResponse.json({
          success: health.healthy,
          message: health.healthy ? "System healthy" : "System has issues",
          health,
          timestamp: new Date().toISOString(),
        });

      case "promote_symbol":
        if (!symbol) {
          return NextResponse.json(
            { error: "symbol is required for promote_symbol action" },
            { status: 400 }
          );
        }

        const promoted = symbolManager.manualPromoteSymbol(symbol);
        return NextResponse.json({
          success: promoted,
          message: promoted
            ? `Symbol ${symbol} promoted to top list`
            : `Symbol ${symbol} is already in top list or promotion failed`,
          timestamp: new Date().toISOString(),
        });

      case "demote_symbol":
        if (!symbol) {
          return NextResponse.json(
            { error: "symbol is required for demote_symbol action" },
            { status: 400 }
          );
        }

        const demoted = symbolManager.manualDemoteSymbol(symbol);
        return NextResponse.json({
          success: demoted,
          message: demoted
            ? `Symbol ${symbol} demoted from top list`
            : `Symbol ${symbol} is not in top list or demotion failed`,
          timestamp: new Date().toISOString(),
        });

      case "analyze_promotion":
        const popularSymbols = cache.getPopularSymbols(100);
        const analysisResult = await symbolManager.analyzeAndPromoteSymbols(
          popularSymbols
        );

        return NextResponse.json({
          success: true,
          message: `Analysis complete: ${analysisResult.promoted.length} promoted, ${analysisResult.demoted.length} demoted`,
          result: analysisResult,
          timestamp: new Date().toISOString(),
        });

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error in cache control:", error);
    return NextResponse.json(
      { error: "Failed to process cache control request" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/cache/control
 * Get available control actions
 */
export async function GET() {
  return NextResponse.json({
    availableActions: [
      {
        action: "start",
        description: "Start the batch cache updater service",
        parameters: [],
      },
      {
        action: "stop",
        description: "Stop the batch cache updater service",
        parameters: [],
      },
      {
        action: "update_batch",
        description: "Manually update a specific batch",
        parameters: ["batchIndex: number"],
      },
      {
        action: "update_all",
        description: "Force update all batches",
        parameters: [],
      },
      {
        action: "health_check",
        description: "Check system health status",
        parameters: [],
      },
      {
        action: "promote_symbol",
        description: "Manually promote a symbol to top 450 list",
        parameters: ["symbol: string"],
      },
      {
        action: "demote_symbol",
        description: "Manually demote a symbol from top 450 list",
        parameters: ["symbol: string"],
      },
      {
        action: "analyze_promotion",
        description: "Run dynamic promotion analysis based on popularity",
        parameters: [],
      },
    ],
    examples: [
      {
        description: "Start the updater",
        method: "POST",
        body: { action: "start" },
      },
      {
        description: "Update batch 0",
        method: "POST",
        body: { action: "update_batch", batchIndex: 0 },
      },
      {
        description: "Force update all batches",
        method: "POST",
        body: { action: "update_all" },
      },
    ],
    timestamp: new Date().toISOString(),
  });
}
