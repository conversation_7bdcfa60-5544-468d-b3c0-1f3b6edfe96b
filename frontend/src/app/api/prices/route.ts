import { NextRequest, NextResponse } from "next/server";
import { getPricesFromSupabase } from "@/lib/services/supabase-price-service";
import {
  getEnhancedServerCache,
  generatePriceCacheKey,
} from "@/lib/services/server-cache";
import { getSymbolManager } from "@/lib/services/symbol-manager";

/**
 * GET /api/prices
 * Query parameters:
 * - symbols: Comma-separated list of symbols to fetch prices for
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  // Get symbols from query parameters
  const symbolsParam = searchParams.get("symbols");

  if (!symbolsParam) {
    return NextResponse.json(
      { error: "Symbols parameter is required" },
      { status: 400 }
    );
  }

  // Parse symbols
  const symbols = symbolsParam
    .split(",")
    .map((s) => s.trim())
    .filter(Boolean);

  if (symbols.length === 0) {
    return NextResponse.json(
      { error: "No valid symbols provided" },
      { status: 400 }
    );
  }

  if (symbols.length > 100) {
    return NextResponse.json(
      { error: "Too many symbols. Maximum 100 allowed per request." },
      { status: 400 }
    );
  }

  try {
    // Get enhanced server cache instance
    const cache = getEnhancedServerCache();
    const symbolManager = getSymbolManager();

    // Check cache for each symbol
    const cacheKeys = symbols.map(generatePriceCacheKey);
    const { cached, missing } = cache.getMultiple(cacheKeys);

    // Convert cache keys back to symbols for missing items
    const missingSymbols = missing.map((key) => key.replace("price:", ""));

    let freshData: Record<string, any> = {};

    // Fetch missing symbols from Supabase
    if (missingSymbols.length > 0) {
      console.log(
        `Cache miss for ${missingSymbols.length} symbols: ${missingSymbols.join(
          ", "
        )}`
      );
      freshData = await getPricesFromSupabase(missingSymbols);

      // Cache the fresh data (5 minute TTL)
      const cacheData: Record<string, any> = {};
      for (const [symbol, data] of Object.entries(freshData)) {
        cacheData[generatePriceCacheKey(symbol)] = data;
      }
      cache.setMultiple(cacheData, 5 * 60 * 1000); // 5 minutes
    } else {
      console.log(`Cache hit for all ${symbols.length} symbols`);
    }

    // Combine cached and fresh data
    const allPrices: Record<string, any> = {};

    // Add cached data
    for (const [cacheKey, data] of Object.entries(cached)) {
      const symbol = cacheKey.replace("price:", "");
      allPrices[symbol] = data;
    }

    // Add fresh data
    Object.assign(allPrices, freshData);

    // Get enhanced cache statistics
    const enhancedStats = cache.getEnhancedStats();

    // Categorize symbols by batch status
    const topSymbols = symbols.filter((s) => symbolManager.isTopSymbol(s));
    const nonTopSymbols = symbols.filter((s) => !symbolManager.isTopSymbol(s));

    return NextResponse.json({
      prices: allPrices,
      timestamp: new Date().toISOString(),
      cacheStats: {
        cached: Object.keys(cached).length,
        fetched: missingSymbols.length,
        total: symbols.length,
        hitRate:
          symbols.length > 0
            ? (Object.keys(cached).length / symbols.length) * 100
            : 0,
        topSymbols: topSymbols.length,
        nonTopSymbols: nonTopSymbols.length,
        enhancedStats,
      },
    });
  } catch (error) {
    console.error("Error processing price request:", error);
    return NextResponse.json(
      { error: "Failed to fetch prices" },
      { status: 500 }
    );
  }
}
