/**
 * Test API for Extended Hours Logic
 * Tests the extended hours functionality (9:00 AM - 4:30 PM ET)
 */

import { NextResponse } from "next/server";
import { getMarketAwareUpdater } from "@/lib/services/market-aware-updater";

export async function GET() {
  try {
    const marketUpdater = getMarketAwareUpdater();
    const marketStatus = marketUpdater.getMarketStatus();
    const inExtendedHours = marketUpdater.isInExtendedHours();
    const shouldUpdateExtended = marketUpdater.shouldUpdateExtended();
    const statusMessage = marketUpdater.getStatusMessage();

    // Test different times to show the logic
    const testTimes = [
      { time: "8:30 AM ET", hour: 8, minute: 30, description: "Before extended hours" },
      { time: "9:00 AM ET", hour: 9, minute: 0, description: "Extended hours start (pre-market)" },
      { time: "9:30 AM ET", hour: 9, minute: 30, description: "Market open" },
      { time: "4:00 PM ET", hour: 16, minute: 0, description: "Market close" },
      { time: "4:30 PM ET", hour: 16, minute: 30, description: "Extended hours end (post-market)" },
      { time: "5:00 PM ET", hour: 17, minute: 0, description: "After extended hours" },
    ];

    const testResults = testTimes.map(test => {
      // Simulate the time check logic
      const isInExtended = 
        (test.hour > 9 || (test.hour === 9 && test.minute >= 0)) &&
        (test.hour < 16 || (test.hour === 16 && test.minute < 30));
      
      const isInMarket = 
        (test.hour > 9 || (test.hour === 9 && test.minute >= 30)) &&
        (test.hour < 16 || (test.hour === 16 && test.minute < 0));

      return {
        time: test.time,
        description: test.description,
        inExtendedHours: isInExtended,
        inMarketHours: isInMarket,
        wouldUpdate: isInExtended, // Open positions would update during extended hours
      };
    });

    return NextResponse.json({
      current: {
        marketStatus,
        inExtendedHours,
        shouldUpdateExtended,
        statusMessage,
      },
      extendedHoursConfig: {
        extendedStart: "9:00 AM ET",
        marketStart: "9:30 AM ET", 
        marketEnd: "4:00 PM ET",
        extendedEnd: "4:30 PM ET",
        description: "Extended hours include 30 minutes before market open and 30 minutes after market close",
      },
      testScenarios: testResults,
      explanation: {
        openPositions: "Will auto-refresh during extended hours (9:00 AM - 4:30 PM ET)",
        closedPositions: "Will auto-refresh during market hours + weekdays with relaxed restrictions",
        weekends: "No auto-refresh for any positions",
        holidays: "No auto-refresh for any positions",
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error testing extended hours:", error);
    return NextResponse.json(
      { error: "Failed to test extended hours logic" },
      { status: 500 }
    );
  }
}
