import { NextResponse } from "next/server";
// Temporarily disabled to fix server/client boundary issues
// import { fetchStockPrice, batchFetchStockPrices, getMarketStatus } from '@/lib/services/yahoo-finance';
// import { getCachedPrice, getAllCachedPrices, getCacheStats } from '@/lib/services/price-cache';
// import { normalizeSymbol } from '@/lib/services/symbol-normalizer';

/**
 * GET /api/stock-price
 * Query parameters:
 * - symbol: Single stock symbol to fetch
 * - symbols: Comma-separated list of symbols to fetch in batch
 * - info: If 'true', returns market status and cache info
 */
export async function GET() {
  // Temporarily disabled to fix server/client boundary issues
  return NextResponse.json(
    {
      error: "Stock price API temporarily disabled",
      message: "Please use /api/prices endpoint instead",
    },
    { status: 503 }
  );
}
