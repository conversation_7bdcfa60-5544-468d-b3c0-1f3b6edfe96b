"use client";

import { useState } from "react";
import { CentralizedStockPrice } from "@/components/stock/centralized-stock-price";
import { SymbolSearch } from "@/components/stock/symbol-search";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function TestPricesPage() {
  const [symbols, setSymbols] = useState<string[]>([]);
  const [newSymbol, setNewSymbol] = useState<string>("");

  const handleAddSymbol = () => {
    if (newSymbol && !symbols.includes(newSymbol)) {
      setSymbols([...symbols, newSymbol]);
      setNewSymbol("");
    }
  };

  const handleSelectSymbol = (symbol: string) => {
    setNewSymbol(symbol);
  };

  const handleRemoveSymbol = (symbol: string) => {
    setSymbols(symbols.filter((s) => s !== symbol));
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Stock Price Test Page</h1>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Add Symbols</h2>
        <div className="flex flex-col space-y-4">
          <SymbolSearch onSelect={handleSelectSymbol} />
          <div className="flex space-x-2">
            <Input
              value={newSymbol}
              onChange={(e) => setNewSymbol(e.target.value)}
              placeholder="Enter a symbol"
              className="flex-1"
            />
            <Button onClick={handleAddSymbol}>Add</Button>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Current Symbols</h2>
        {symbols.length === 0 ? (
          <p className="text-gray-500">No symbols added yet.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {symbols.map((symbol) => (
              <div
                key={symbol}
                className="border rounded-lg p-4 flex justify-between items-center"
              >
                <div>
                  <div className="font-medium">{symbol}</div>
                  <CentralizedStockPrice
                    symbol={symbol}
                    showChange={true}
                    showRefreshButton={true}
                  />
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveSymbol(symbol)}
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Duplicate Symbol Test</h2>
        <p className="mb-4">
          This section displays the same symbol multiple times to demonstrate that
          only one API call is made.
        </p>
        {symbols.length === 0 ? (
          <p className="text-gray-500">Add a symbol first to test.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(3)].map((_, index) => (
              <div
                key={`${symbols[0]}-${index}`}
                className="border rounded-lg p-4"
              >
                <div className="font-medium">
                  {symbols[0]} (Instance {index + 1})
                </div>
                <CentralizedStockPrice
                  symbol={symbols[0]}
                  showChange={true}
                  showRefreshButton={true}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
