import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "sonner";
import { AuthProvider } from "@/lib/auth/auth-context";
import { PriceContextProvider } from "@/lib/contexts/price-context";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Five Rivers Options - Options Tracking and Journaling Platform",
  description:
    "Track, analyze, and improve your options trading performance with Five Rivers Options.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <PriceContextProvider>
            {children}
            <Toaster position="top-right" />
          </PriceContextProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
