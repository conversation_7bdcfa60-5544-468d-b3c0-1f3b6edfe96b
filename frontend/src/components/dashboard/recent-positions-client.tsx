"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { CentralizedStockPrice } from "@/components/stock/centralized-stock-price";
import { Position } from "@/types/position";
import { formatDate } from "@/lib/utils/date-formatter";
import { DTEDisplayClient } from "@/components/positions/dte-display-client";

interface RecentPositionsClientProps {
  positions: Position[];
}

export function RecentPositionsClient({
  positions,
}: RecentPositionsClientProps) {
  if (positions.length === 0) {
    return (
      <div className="text-center py-6">
        <p className="text-muted-foreground">No positions found</p>
        <Button className="mt-4" asChild>
          <Link href="/dashboard/positions/new">
            Create your first position
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b text-sm text-muted-foreground">
            <th className="px-4 py-3 text-left">Symbol</th>
            <th className="px-4 py-3 text-left">Strategy</th>
            <th className="px-4 py-3 text-left">Stock Price Now</th>
            <th className="px-4 py-3 text-left">Open Date</th>
            <th className="px-4 py-3 text-left">Expiry</th>
            <th className="px-4 py-3 text-left">Status</th>
            <th className="px-4 py-3 text-right">P&L</th>
            <th className="px-4 py-3 text-right">Actions</th>
          </tr>
        </thead>
        <tbody>
          {positions.map((position) => (
            <tr key={position.id} className="border-b hover:bg-muted/50">
              <td className="px-4 py-3">
                <div className="flex flex-col">
                  <span className="font-medium">
                    {position.symbol.toUpperCase()}
                  </span>
                  <CentralizedStockPrice
                    symbol={position.symbol}
                    showName={true}
                    className="mt-1"
                  />
                </div>
              </td>
              <td className="px-4 py-3">{position.strategy_type}</td>
              <td className="px-4 py-3">
                <CentralizedStockPrice
                  symbol={position.symbol}
                  showChange={true}
                  showRefreshButton={true}
                />
              </td>
              <td className="px-4 py-3">
                {formatDate(position.open_date, "yyyy-MM-dd")}
              </td>
              <td className="px-4 py-3">
                <div className="flex flex-col">
                  <DTEDisplayClient
                    expiryDate={position.expiry_date}
                    status={position.status}
                  />
                  <span className="text-xs text-muted-foreground">
                    {formatDate(position.expiry_date, "yyyy-MM-dd")}
                  </span>
                </div>
              </td>
              <td className="px-4 py-3">
                <span
                  className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                    position.status === "open"
                      ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                      : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                  }`}
                >
                  {position.status}
                </span>
              </td>
              <td
                className={`px-4 py-3 text-right ${
                  (position.profit_loss || 0) >= 0
                    ? "text-green-600 dark:text-green-400"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {position.profit_loss !== null
                  ? `$${position.profit_loss.toFixed(2)}`
                  : "-"}
              </td>
              <td className="px-4 py-3 text-right">
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/dashboard/positions/${position.id}`}>View</Link>
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div className="flex justify-end mt-4">
        <Button variant="outline" asChild>
          <Link href="/dashboard/positions">View All Positions</Link>
        </Button>
      </div>
    </div>
  );
}
