"use client";

import { ReactNode } from "react";
import { AnimatedBlob } from "./animated-blob";

interface GradientBackgroundProps {
  children?: ReactNode;
  className?: string;
}

export function GradientBackground({
  children = null,
  className = "",
}: GradientBackgroundProps) {
  return (
    <div
      className={`absolute inset-0 min-h-screen w-full bg-gradient-to-b from-[#12002a] via-[#0e0022] via-70% to-[#12002a] ${className}`}
    >
      {/* Animated blurred blobs for background depth */}
      <AnimatedBlob
        size="60vh"
        color="bg-purple-700/20"
        initialPosition={{ left: "-20px", top: "-20px" }}
        animationDuration={30}
      />
      <AnimatedBlob
        size="80vh"
        color="bg-indigo-600/20"
        initialPosition={{ right: "10px", top: "60px" }}
        animationDuration={40}
      />
      <AnimatedBlob
        size="40vh"
        color="bg-violet-500/20"
        initialPosition={{ bottom: "20px", left: "33%" }}
        animationDuration={25}
      />

      {/* Additional animated blurred elements for more depth and visual interest */}
      <AnimatedBlob
        size="96vh"
        color="bg-purple-900/10"
        initialPosition={{ top: "25%", left: "25%" }}
        animationDuration={50}
      />
      <AnimatedBlob
        size="72vh"
        color="bg-indigo-800/15"
        initialPosition={{ right: "-20px", bottom: "40px" }}
        animationDuration={35}
      />
      <AnimatedBlob
        size="48vh"
        color="bg-violet-700/10"
        initialPosition={{ top: "50%", right: "33%" }}
        animationDuration={45}
      />

      {/* Darker gradient overlay for better text contrast */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-black/10 pointer-events-none"></div>

      {children}
    </div>
  );
}
