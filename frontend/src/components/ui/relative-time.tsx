"use client";

import { useEffect, useState } from "react";
import { formatRelativeTime } from "@/lib/utils/date-formatter";

interface RelativeTimeProps {
  date: string;
  className?: string;
}

/**
 * Client-only component for displaying relative time (e.g., "2 days ago")
 * This avoids hydration errors by only rendering on the client
 */
export function RelativeTime({ date, className = "text-xs text-muted-foreground" }: RelativeTimeProps) {
  const [relativeTimeText, setRelativeTimeText] = useState<string>("");
  
  useEffect(() => {
    // Calculate relative time on the client only
    setRelativeTimeText(formatRelativeTime(date));
    
    // Optional: Set up an interval to refresh the relative time periodically
    const intervalId = setInterval(() => {
      setRelativeTimeText(formatRelativeTime(date));
    }, 60000); // Update every minute
    
    return () => clearInterval(intervalId);
  }, [date]);
  
  // Return empty span during SSR to avoid hydration mismatch
  if (!relativeTimeText) {
    return <span className={className}></span>;
  }
  
  return <span className={className}>{relativeTimeText}</span>;
}
