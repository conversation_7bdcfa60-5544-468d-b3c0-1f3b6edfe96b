"use client";

import { useEffect, useState } from "react";

interface AnimatedBlobProps {
  className?: string;
  size: string;
  color: string;
  initialPosition: {
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
  };
  animationDuration?: number;
  animationRange?: number;
}

export function AnimatedBlob({
  className = "",
  size,
  color,
  initialPosition,
  animationDuration = 20,
  animationRange = 20,
}: AnimatedBlobProps) {
  // Use a safer initial state that won't cause rendering issues
  const [position, setPosition] = useState({ ...initialPosition });
  const [direction, setDirection] = useState({
    x: Math.random() > 0.5 ? 1 : -1,
    y: Math.random() > 0.5 ? 1 : -1,
  });

  useEffect(() => {
    // Generate random movement within the specified range
    const moveBlob = () => {
      const randomX = Math.random() * animationRange * direction.x;
      const randomY = Math.random() * animationRange * direction.y;

      // Calculate new position
      const newPosition = { ...initialPosition };

      // Helper function to parse position values and handle percentages
      const parsePosition = (value: string) => {
        if (value.includes("%")) {
          // For percentage values, just return as is
          return { value: parseFloat(value), unit: "%" };
        } else {
          // For pixel values, parse the number
          return { value: parseInt(value), unit: "px" };
        }
      };

      if (initialPosition.left !== undefined) {
        const { value, unit } = parsePosition(initialPosition.left);
        if (!isNaN(value)) {
          newPosition.left = `${value + (unit === "px" ? randomX : 0)}${unit}`;
        }
      }

      if (initialPosition.right !== undefined) {
        const { value, unit } = parsePosition(initialPosition.right);
        if (!isNaN(value)) {
          newPosition.right = `${value - (unit === "px" ? randomX : 0)}${unit}`;
        }
      }

      if (initialPosition.top !== undefined) {
        const { value, unit } = parsePosition(initialPosition.top);
        if (!isNaN(value)) {
          newPosition.top = `${value + (unit === "px" ? randomY : 0)}${unit}`;
        }
      }

      if (initialPosition.bottom !== undefined) {
        const { value, unit } = parsePosition(initialPosition.bottom);
        if (!isNaN(value)) {
          newPosition.bottom = `${
            value - (unit === "px" ? randomY : 0)
          }${unit}`;
        }
      }

      setPosition(newPosition);

      // Randomly change direction occasionally
      if (Math.random() > 0.7) {
        setDirection({
          x: Math.random() > 0.5 ? 1 : -1,
          y: Math.random() > 0.5 ? 1 : -1,
        });
      }
    };

    const interval = setInterval(moveBlob, animationDuration * 1000);
    return () => clearInterval(interval);
  }, [
    initialPosition,
    animationDuration,
    animationRange,
    direction.x,
    direction.y,
  ]);

  const style = {
    ...position,
    width: size,
    height: size,
    transition: `all ${animationDuration}s ease-in-out`,
  };

  return (
    <div
      className={`absolute rounded-full blur-3xl pointer-events-none ${color} ${className}`}
      style={style}
    />
  );
}
