"use client";

import { ReactNode } from "react";

interface DashboardBackgroundProps {
  children?: ReactNode;
  className?: string;
}

export function DashboardBackground({
  children = null,
  className = "",
}: DashboardBackgroundProps) {
  return (
    <div
      className={`absolute inset-0 min-h-screen w-full bg-[#12001e] ${className}`}
    >
      {/* Subtle gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#12001e] via-[#12001e] to-[#0a0012] pointer-events-none"></div>
      
      {/* Very subtle texture overlay */}
      <div className="absolute inset-0 opacity-5 pointer-events-none bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjZmZmIj48L3JlY3Q+CjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiMwMDAiPjwvcmVjdD4KPC9zdmc+')]"></div>
      
      {children}
    </div>
  );
}
