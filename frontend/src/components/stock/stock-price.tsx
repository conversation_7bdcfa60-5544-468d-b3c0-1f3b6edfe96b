"use client";

import { useState, useEffect } from 'react';
import { ArrowUpIcon, ArrowDownIcon, RefreshCwIcon } from 'lucide-react';

interface StockPriceProps {
  symbol: string;
  initialPrice?: number | null;
  refreshInterval?: number; // in milliseconds
  showChange?: boolean;
  className?: string;
}

export function StockPrice({
  symbol,
  initialPrice = null,
  refreshInterval = 60000, // Default: 1 minute
  showChange = false,
  className = '',
}: StockPriceProps) {
  const [price, setPrice] = useState<number | null>(initialPrice);
  const [previousPrice, setPreviousPrice] = useState<number | null>(initialPrice);
  const [loading, setLoading] = useState<boolean>(initialPrice === null);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(initialPrice ? new Date() : null);

  // Function to fetch the current price
  const fetchPrice = async () => {
    if (!symbol) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/stock-price?symbol=${encodeURIComponent(symbol)}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch stock price');
      }
      
      const data = await response.json();
      
      if (data.price !== undefined && data.price !== null) {
        // Store previous price for change indication
        if (price !== null && price !== data.price) {
          setPreviousPrice(price);
        }
        
        setPrice(data.price);
        setLastUpdated(new Date());
      } else {
        setError('No price data available');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error(`Error fetching price for ${symbol}:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch and set up polling
  useEffect(() => {
    // Reset state when symbol changes
    setPrice(initialPrice);
    setPreviousPrice(initialPrice);
    setLoading(initialPrice === null);
    setError(null);
    setLastUpdated(initialPrice ? new Date() : null);
    
    if (!symbol) return;
    
    // Fetch immediately if we don't have an initial price
    if (initialPrice === null) {
      fetchPrice();
    }
    
    // Set up polling
    const intervalId = setInterval(fetchPrice, refreshInterval);
    
    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, [symbol, initialPrice, refreshInterval]);

  // Determine if price went up or down
  const priceDirection = previousPrice !== null && price !== null
    ? (price > previousPrice ? 'up' : price < previousPrice ? 'down' : 'same')
    : 'same';

  // Render loading state
  if (loading && price === null) {
    return (
      <div className={`flex items-center ${className}`}>
        <RefreshCwIcon className="h-4 w-4 animate-spin text-gray-400 mr-2" />
        <span className="text-gray-400">Loading...</span>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`text-red-500 ${className}`} title={error}>
        N/A
      </div>
    );
  }

  // Render price
  return (
    <div className={`flex items-center ${className}`}>
      {price !== null ? (
        <>
          <span 
            className={`font-medium ${
              priceDirection === 'up' 
                ? 'text-green-600' 
                : priceDirection === 'down' 
                  ? 'text-red-600' 
                  : ''
            }`}
          >
            ${price.toFixed(2)}
          </span>
          
          {showChange && priceDirection !== 'same' && (
            <span className="ml-1">
              {priceDirection === 'up' ? (
                <ArrowUpIcon className="h-4 w-4 text-green-600" />
              ) : (
                <ArrowDownIcon className="h-4 w-4 text-red-600" />
              )}
            </span>
          )}
          
          {lastUpdated && (
            <span className="text-xs text-gray-400 ml-2" title={`Last updated: ${lastUpdated.toLocaleTimeString()}`}>
              {Math.floor((Date.now() - lastUpdated.getTime()) / 1000)}s ago
            </span>
          )}
        </>
      ) : (
        <span className="text-gray-400">N/A</span>
      )}
    </div>
  );
}
