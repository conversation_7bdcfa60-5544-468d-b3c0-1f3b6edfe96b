"use client";

import { useState, useEffect } from "react";
import { ArrowUpIcon, ArrowDownIcon, RefreshCwIcon } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { usePriceContext } from "@/lib/contexts/price-context";

interface CentralizedStockPriceProps {
  symbol: string;
  refreshInterval?: number; // in milliseconds (not used with context)
  showChange?: boolean;
  showRefreshButton?: boolean;
  showName?: boolean; // New prop to show the underlying name
  className?: string;
  positionStatus?: "open" | "closed"; // Position status for smart refresh priority
  isVisible?: boolean; // Whether the component is currently visible
}

export function CentralizedStockPrice({
  symbol,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  refreshInterval, // Not used with context
  showChange = false,
  showRefreshButton = false,
  showName = false, // Default to false
  className = "",
  positionStatus, // Position status for smart refresh priority
  isVisible = true, // Default to visible
}: CentralizedStockPriceProps) {
  const {
    priceData: contextPriceData,
    registerSymbol,
    refreshSymbol,
    isLoading,
    isRefreshing,
  } = usePriceContext();
  const [previousPrice, setPreviousPrice] = useState<number | null>(null);
  const [lastRefreshTime, setLastRefreshTime] = useState<number | null>(null);

  // Get the price data for this symbol from context
  const priceData = symbol ? contextPriceData[symbol] : null;
  const loading = symbol ? isLoading[symbol] : false;
  const refreshing = symbol ? isRefreshing[symbol] : false;

  // Handle manual refresh
  const handleRefresh = async () => {
    // Prevent rapid refreshes (reduced from 10s to 5s with global cache)
    if (lastRefreshTime && Date.now() - lastRefreshTime < 5000) {
      alert("Please wait at least 5 seconds between manual refreshes");
      return;
    }

    if (symbol) {
      await refreshSymbol(symbol);
      setLastRefreshTime(Date.now());
    }
  };

  // Register this symbol with the context including position status
  useEffect(() => {
    if (symbol) {
      registerSymbol(symbol, positionStatus, isVisible);
    }
  }, [symbol, registerSymbol, positionStatus, isVisible]);

  // Update previous price when price changes
  useEffect(() => {
    if (priceData?.price !== undefined) {
      if (previousPrice !== null && priceData.price !== previousPrice) {
        setPreviousPrice(priceData.price);
      } else if (previousPrice === null) {
        setPreviousPrice(priceData.price);
      }
    }
  }, [priceData, previousPrice]);

  // Determine if price went up or down
  const priceDirection =
    previousPrice !== null &&
    priceData?.price !== undefined &&
    priceData.price !== null
      ? priceData.price > previousPrice
        ? "up"
        : priceData.price < previousPrice
        ? "down"
        : "same"
      : "same";

  // Format the last updated time
  const formattedLastUpdated = priceData?.lastUpdated
    ? formatDistanceToNow(new Date(priceData.lastUpdated), { addSuffix: true })
    : null;

  // Only show loading state when there's no data yet
  if (loading && !priceData) {
    return (
      <div className={`flex items-center ${className}`}>
        <span className="text-gray-400">Loading...</span>
      </div>
    );
  }

  // If showName is true and we have a name, only show the name
  if (showName && priceData?.name) {
    return (
      <div className={`flex flex-col ${className}`}>
        <span className="text-xs text-gray-500">{priceData.name}</span>
      </div>
    );
  }

  // Otherwise, render price
  return (
    <div className={`flex flex-col ${className}`}>
      <div className="flex items-center">
        {priceData?.price !== undefined ? (
          <>
            <span
              className={`font-medium ${
                priceDirection === "up"
                  ? "text-green-600"
                  : priceDirection === "down"
                  ? "text-red-600"
                  : ""
              }`}
            >
              ${priceData.price !== null ? priceData.price.toFixed(2) : "N/A"}
            </span>

            {showChange &&
              priceData.changePercent !== undefined &&
              priceData.changePercent !== null && (
                <span
                  className={`ml-2 text-sm ${
                    priceData.changePercent > 0
                      ? "text-green-600"
                      : priceData.changePercent < 0
                      ? "text-red-600"
                      : "text-gray-500"
                  }`}
                >
                  {priceData.changePercent > 0 ? "+" : ""}
                  {priceData.changePercent.toFixed(2)}%
                  {priceData.changePercent > 0 ? (
                    <ArrowUpIcon className="inline h-3 w-3 ml-1" />
                  ) : priceData.changePercent < 0 ? (
                    <ArrowDownIcon className="inline h-3 w-3 ml-1" />
                  ) : null}
                </span>
              )}

            {showRefreshButton && (
              <button
                onClick={handleRefresh}
                disabled={loading || refreshing}
                className="ml-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                title="Refresh price"
              >
                <RefreshCwIcon
                  className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
                />
              </button>
            )}
          </>
        ) : (
          <span className="text-gray-400">N/A</span>
        )}
      </div>

      {formattedLastUpdated && (
        <span className="text-xs text-gray-400 mt-1">
          Updated {formattedLastUpdated}
        </span>
      )}
    </div>
  );
}
