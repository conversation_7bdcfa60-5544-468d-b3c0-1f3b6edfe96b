"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { RefreshCw, Activity, Database, Users } from "lucide-react";

interface CacheStats {
  cache: {
    totalEntries: number;
    hitRate: string;
    totalHits: number;
    totalMisses: number;
    status: string;
  };
  system: {
    type: string;
    ttl: string;
    cleanupInterval: string;
  };
  timestamp: string;
}

export function CacheMonitor() {
  const [stats, setStats] = useState<CacheStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/cache/stats");
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch stats");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, []);

  if (error) {
    return (
      <Card className="bg-red-50 border-red-200">
        <CardContent className="p-4">
          <p className="text-red-600">Error loading cache stats: {error}</p>
          <Button onClick={fetchStats} className="mt-2" size="sm">
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Cache Monitor</h2>
        <Button
          onClick={fetchStats}
          disabled={loading}
          size="sm"
          variant="outline"
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Cache Overview */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Cache Overview
              </CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.cache.totalEntries}
              </div>
              <p className="text-xs text-muted-foreground">Total Entries</p>
              <div className="mt-2 space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Hits:</span>
                  <Badge variant="outline" className="text-green-600">
                    {stats.cache.totalHits}
                  </Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Misses:</span>
                  <Badge variant="outline" className="text-orange-600">
                    {stats.cache.totalMisses}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Hit Rate */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats.cache.hitRate}
              </div>
              <p className="text-xs text-muted-foreground">Cache Efficiency</p>
              <div className="mt-2">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{
                      width: stats.cache.hitRate,
                    }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Info */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Info</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.system.type}</div>
              <p className="text-xs text-muted-foreground">Cache Type</p>
              <div className="mt-2">
                <Badge variant="outline" className="text-blue-600">
                  TTL: {stats.system.ttl}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Cache Status */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Cache Status
              </CardTitle>
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={
                      stats.cache.status === "active" ? "default" : "secondary"
                    }
                  >
                    {stats.cache.status}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  Cleanup: {stats.system.cleanupInterval}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {stats && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">System Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Last Updated:</span>
                <p className="text-muted-foreground">
                  {new Date(stats.timestamp).toLocaleString()}
                </p>
              </div>
              <div>
                <span className="font-medium">Cache Efficiency:</span>
                <p className="text-green-600 font-medium">
                  {stats.cache.totalHits > 0
                    ? `${Math.round(
                        (stats.cache.totalHits /
                          (stats.cache.totalHits + stats.cache.totalMisses)) *
                          100
                      )}% hit rate`
                    : "No data yet"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
