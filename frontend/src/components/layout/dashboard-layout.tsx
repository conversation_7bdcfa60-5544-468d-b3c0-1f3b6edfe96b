"use client";

import { ReactNode } from "react";
import Header from "./header";
import Sidebar from "./sidebar";
import { DashboardBackground } from "../ui/dashboard-background";

interface DashboardLayoutProps {
  children: ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="relative min-h-screen">
      <div className="absolute inset-0 z-0">
        <DashboardBackground />
      </div>
      <div className="relative z-10 min-h-screen flex flex-col">
        <Header />
        <div className="flex flex-1">
          <Sidebar />
          <main className="flex-1 p-4 md:p-6 overflow-auto">{children}</main>
        </div>
      </div>
    </div>
  );
}
