"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UserCircle } from "lucide-react";
import { useAuth } from "@/lib/auth/auth-context";

export default function Header() {
  const { authState, signOut } = useAuth();
  const isLoggedIn = !!authState.user;

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-purple-500/20 bg-[#12002a]/45 backdrop-blur-md text-white">
      <div className="container flex h-18 items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/" className="flex items-center space-x-2">
            <span className="text-xl font-bold text-white hover:text-purple-200">
              Five Rivers Options
            </span>
          </Link>
        </div>

        <nav className="hidden md:flex items-center gap-6">
          <Link
            href="/"
            className="text-sm font-medium text-white hover:text-purple-200"
          >
            Home
          </Link>
          {isLoggedIn && (
            <>
              <Link
                href="/dashboard"
                className="text-sm font-medium text-white hover:text-purple-200"
              >
                Dashboard
              </Link>
              <Link
                href="/dashboard/positions"
                className="text-sm font-medium text-white hover:text-purple-200"
              >
                Positions
              </Link>
              <Link
                href="/dashboard/settings"
                className="text-sm font-medium text-white hover:text-purple-200"
              >
                Settings
              </Link>
            </>
          )}
        </nav>

        <div className="flex items-center gap-2">
          {isLoggedIn ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-full text-white hover:text-purple-200 hover:bg-purple-800/30"
                >
                  <UserCircle className="h-6 w-6" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleSignOut}>
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <>
              <Button
                asChild
                variant="ghost"
                className="text-white hover:text-purple-200 hover:bg-purple-800/30"
              >
                <Link href="/login">Log in</Link>
              </Button>
              <Button asChild className="bg-purple-700 hover:bg-purple-800">
                <Link href="/register">Sign up</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
