import { ReactNode } from "react";
import Header from "./header";
import Footer from "./footer";
import { GradientBackground } from "../ui/gradient-background";

interface LandingLayoutProps {
  children: ReactNode;
}

export default function LandingLayout({ children }: LandingLayoutProps) {
  return (
    <div className="relative min-h-screen">
      <div className="absolute inset-0 z-0">
        <GradientBackground />
      </div>
      <div className="relative z-10 min-h-screen flex flex-col">
        <Header />
        <main className="flex-1">{children}</main>
        <Footer />
      </div>
    </div>
  );
}
