"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

export function AnimatedHero() {
  return (
    <div className="flex flex-col lg:flex-row items-center gap-12">
      <motion.div 
        className="max-w-3xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7 }}
      >
        <motion.h1 
          className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          Trade Smarter with{" "}
          <motion.span 
            className="text-emerald-400"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            Five Rivers Options
          </motion.span>
        </motion.h1>
        <motion.p 
          className="mt-6 body-large text-teal-100"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.7 }}
        >
          Track, analyze, and improve your options trading performance
          through detailed journaling and powerful analytics.
        </motion.p>
        <motion.div 
          className="mt-10 flex flex-wrap gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.9 }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              asChild
              size="lg"
              className="bg-teal-600 hover:bg-teal-700"
            >
              <Link href="/register">Get Started</Link>
            </Button>
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              asChild
              variant="outline"
              size="lg"
              className="text-teal-600 hover:text-white"
            >
              <Link href="/login">Log In</Link>
            </Button>
          </motion.div>
        </motion.div>
      </motion.div>
      
      {/* Trading Chart Illustration */}
      <motion.div 
        className="lg:flex-1 w-full max-w-lg mx-auto mt-8 lg:mt-0"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.7, delay: 0.5 }}
      >
        <motion.div 
          className="relative backdrop-blur-md bg-white/10 rounded-3xl p-6 shadow-lg border border-white/20 overflow-hidden"
          initial={{ y: 20 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          whileHover={{ y: -5, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
        >
          <div className="absolute top-4 left-4 flex space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
          <div className="pt-6">
            {/* Chart Header */}
            <div className="flex justify-between items-center mb-4">
              <div>
                <div className="text-emerald-400 font-bold">AAPL</div>
                <div className="text-sm text-teal-100">Apple Inc.</div>
              </div>
              <div className="text-right">
                <div className="text-emerald-400 font-bold">$182.63</div>
                <div className="text-sm text-emerald-300">+2.41 (1.32%)</div>
              </div>
            </div>
            
            {/* SVG Chart */}
            <svg
              className="w-full h-48"
              viewBox="0 0 400 120"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* Grid Lines */}
              <line x1="0" y1="0" x2="400" y2="0" stroke="#ffffff20" strokeDasharray="4 4" />
              <line x1="0" y1="30" x2="400" y2="30" stroke="#ffffff20" strokeDasharray="4 4" />
              <line x1="0" y1="60" x2="400" y2="60" stroke="#ffffff20" strokeDasharray="4 4" />
              <line x1="0" y1="90" x2="400" y2="90" stroke="#ffffff20" strokeDasharray="4 4" />
              <line x1="0" y1="120" x2="400" y2="120" stroke="#ffffff20" strokeDasharray="4 4" />
              
              {/* Chart Line */}
              <path
                d="M0,100 C20,90 40,110 60,80 C80,50 100,60 120,40 C140,20 160,30 180,20 C200,10 220,30 240,20 C260,10 280,30 300,40 C320,50 340,30 360,40 C380,50 400,30 400,30"
                stroke="#34d399"
                strokeWidth="2"
                fill="none"
              />
              
              {/* Area under the curve */}
              <path
                d="M0,100 C20,90 40,110 60,80 C80,50 100,60 120,40 C140,20 160,30 180,20 C200,10 220,30 240,20 C260,10 280,30 300,40 C320,50 340,30 360,40 C380,50 400,30 400,30 L400,120 L0,120 Z"
                fill="url(#gradient)"
                opacity="0.2"
              />
              
              {/* Gradient definition */}
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="#34d399" />
                  <stop offset="100%" stopColor="#34d39900" />
                </linearGradient>
              </defs>
            </svg>
            
            {/* Time Labels */}
            <div className="flex justify-between text-xs text-teal-200 mt-2">
              <div>9:30 AM</div>
              <div>11:00 AM</div>
              <div>12:30 PM</div>
              <div>2:00 PM</div>
              <div>4:00 PM</div>
            </div>
            
            {/* Options Data */}
            <div className="mt-6 pt-4 border-t border-white/10">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-2 rounded bg-white/5">
                  <div className="text-xs text-teal-200">Call Option</div>
                  <div className="text-emerald-400 font-mono">$185c 06/21</div>
                  <div className="text-xs text-emerald-300">+24.5%</div>
                </div>
                <div className="text-center p-2 rounded bg-white/5">
                  <div className="text-xs text-teal-200">Put Option</div>
                  <div className="text-red-400 font-mono">$180p 06/21</div>
                  <div className="text-xs text-red-300">-12.3%</div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
