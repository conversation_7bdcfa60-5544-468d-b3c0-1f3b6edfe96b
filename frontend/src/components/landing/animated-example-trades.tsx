"use client";

import { motion } from "framer-motion";

export function AnimatedExampleTrades() {
  return (
    <>
      <motion.h2 
        className="h2 text-center text-white mb-12"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7 }}
        viewport={{ once: true }}
      >
        Trade Management Made Simple
      </motion.h2>

      <motion.div 
        className="backdrop-blur-md bg-white/10 rounded-3xl p-8 shadow-lg border border-white/20 max-w-4xl mx-auto overflow-hidden"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7, delay: 0.2 }}
        viewport={{ once: true }}
        whileHover={{ y: -5, boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" }}
      >
        <div className="overflow-x-auto">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b border-teal-400/30">
                <th className="p-4 text-teal-300">Stock Ticker</th>
                <th className="p-4 text-teal-300">Strategy</th>
                <th className="p-4 text-teal-300">Cost Basis</th>
                <th className="p-4 text-teal-300">P&L</th>
              </tr>
            </thead>
            <tbody className="text-white">
              <tr className="border-b border-teal-400/20 hover:bg-white/5">
                <td className="p-4">AAPL</td>
                <td className="p-4">Call Spread</td>
                <td className="p-4">$240.50</td>
                <td className="p-4 text-green-400">+$82.25</td>
              </tr>
              <tr className="border-b border-teal-400/20 hover:bg-white/5">
                <td className="p-4">TSLA</td>
                <td className="p-4">Iron Condor</td>
                <td className="p-4">$325.75</td>
                <td className="p-4 text-red-400">-$42.50</td>
              </tr>
              <tr className="border-b border-teal-400/20 hover:bg-white/5">
                <td className="p-4">AMZN</td>
                <td className="p-4">Put Option</td>
                <td className="p-4">$180.00</td>
                <td className="p-4 text-green-400">+$65.00</td>
              </tr>
            </tbody>
          </table>
        </div>
      </motion.div>
    </>
  );
}
