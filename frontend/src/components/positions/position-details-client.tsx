"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  <PERSON><PERSON><PERSON>,
  BookOpen,
  Calendar,
  Edit,
  Hash,
  Tag,
  Target,
  DollarSign,
} from "lucide-react";
import { formatDate } from "@/lib/utils/date-formatter";
import { DTEDisplayClient } from "@/components/positions/dte-display-client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ClosePositionDialog } from "@/components/positions/close-position-dialog";
import { DeletePositionDialog } from "@/components/positions/delete-position-dialog";
import { Position } from "@/types/position";

interface PositionDetailsClientProps {
  position: Position;
}

export function PositionDetailsClient({
  position,
}: PositionDetailsClientProps) {
  const router = useRouter();

  const formatDateDisplay = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return formatDate(dateString, "MMMM d, yyyy");
  };

  const handlePositionClosed = () => {
    router.refresh();
  };

  const handlePositionDeleted = () => {
    router.push("/dashboard/positions");
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-white">
            {position.symbol.toUpperCase()} - {position.strategy_type}
          </h1>
          <p className="text-white/70">
            Opened on {formatDateDisplay(position.open_date)}
          </p>
          <div className="mt-2">
            <Badge
              variant={position.status === "open" ? "outline" : "secondary"}
              className={
                position.status === "open"
                  ? "border-green-500 text-green-400 bg-green-500/10"
                  : "border-gray-500 text-gray-300 bg-gray-500/10"
              }
            >
              {position.status.toUpperCase()}
            </Badge>
          </div>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/dashboard/positions/${position.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>

          {position.status === "open" ? (
            <ClosePositionDialog
              position={position}
              onClose={handlePositionClosed}
            />
          ) : (
            <Button variant="outline" asChild>
              <Link href={`/dashboard/positions/${position.id}/analytics`}>
                <BarChart className="mr-2 h-4 w-4" />
                Analytics
              </Link>
            </Button>
          )}

          <DeletePositionDialog
            position={position}
            onDelete={handlePositionDeleted}
          />
        </div>
      </div>

      {/* Position details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-[#1a1025] border-[#3d2a50]/30 rounded-lg border shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4 text-white">
            Position Details
          </h2>
          <div className="space-y-4">
            <div className="flex items-start gap-2">
              <Hash className="h-4 w-4 mt-1 text-white/70" />
              <div>
                <p className="font-medium text-white">Symbol</p>
                <p className="text-white">{position.symbol.toUpperCase()}</p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <BookOpen className="h-4 w-4 mt-1 text-white/70" />
              <div>
                <p className="font-medium text-white">Strategy</p>
                <p className="text-white">{position.strategy_type}</p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <Target className="h-4 w-4 mt-1 text-white/70" />
              <div>
                <p className="font-medium text-white">Strike Prices</p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {position.strike_prices.map((price: number, i: number) => (
                    <span
                      key={i}
                      className="text-xs font-mono bg-[#2d2040] text-white p-1 rounded"
                    >
                      ${price}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <DollarSign className="h-4 w-4 mt-1 text-white/70" />
              <div>
                <p className="font-medium text-white">
                  {position.credit_debit_amount >= 0 ? "Credit" : "Debit"}{" "}
                  Amount
                </p>
                <p
                  className={
                    position.credit_debit_amount >= 0
                      ? "text-green-600"
                      : "text-red-600"
                  }
                >
                  ${Math.abs(position.credit_debit_amount).toFixed(2)}
                </p>
              </div>
            </div>
            {position.status === "closed" && position.profit_loss !== null && (
              <div className="flex items-start gap-2">
                <DollarSign className="h-4 w-4 mt-1 text-white/70" />
                <div>
                  <p className="font-medium text-white">Profit/Loss</p>
                  <p
                    className={
                      (position.profit_loss || 0) >= 0
                        ? "text-green-600 font-medium"
                        : "text-red-600 font-medium"
                    }
                  >
                    {(position.profit_loss || 0) >= 0 ? "+" : ""}$
                    {(position.profit_loss || 0).toFixed(2)}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="bg-[#1a1025] border-[#3d2a50]/30 rounded-lg border shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4 text-white">Timeline</h2>
          <div className="space-y-4">
            <div className="flex items-start gap-2">
              <Calendar className="h-4 w-4 mt-1 text-white/70" />
              <div>
                <p className="font-medium text-white">Open Date</p>
                <p className="text-white">
                  {formatDateDisplay(position.open_date)}
                </p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <Calendar className="h-4 w-4 mt-1 text-white/70" />
              <div>
                <p className="font-medium text-white">Expiration</p>
                <DTEDisplayClient
                  expiryDate={position.expiry_date}
                  status={position.status}
                />
                <p className="text-xs text-white/70">
                  {formatDateDisplay(position.expiry_date)}
                </p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <Calendar className="h-4 w-4 mt-1 text-white/70" />
              <div>
                <p className="font-medium text-white">Close Date</p>
                <p className="text-white">
                  {position.close_date
                    ? formatDateDisplay(position.close_date)
                    : "Not closed"}
                </p>
              </div>
            </div>
            {position.tags && position.tags.length > 0 && (
              <div className="flex items-start gap-2">
                <Tag className="h-4 w-4 mt-1 text-white/70" />
                <div>
                  <p className="font-medium text-white">Tags</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {Array.isArray(position.tags) &&
                      position.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs text-white bg-gray-600/30 border-gray-500"
                        >
                          {typeof tag === "string" ? tag : tag.tag_name}
                        </Badge>
                      ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {position.notes && (
        <div className="bg-[#1a1025] border-[#3d2a50]/30 rounded-lg border shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4 text-white">Notes</h2>
          <p className="whitespace-pre-line text-white">{position.notes}</p>
        </div>
      )}
    </div>
  );
}
