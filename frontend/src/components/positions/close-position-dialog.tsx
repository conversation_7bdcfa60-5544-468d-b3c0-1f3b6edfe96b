"use client";

import { useState } from "react";
import { format } from "date-fns";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { closePosition } from "@/lib/actions/positions";
import { Position } from "@/types/position";
import { closePositionSchema } from "@/lib/validations/position";
import { X } from "lucide-react";

type ClosePositionFormValues = z.infer<typeof closePositionSchema>;

interface ClosePositionDialogProps {
  position: Position;
  onClose: () => void;
  trigger?: React.ReactNode;
}

export function ClosePositionDialog({
  position,
  onClose,
  trigger,
}: ClosePositionDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const today = new Date();
  const formattedToday = format(today, "yyyy-MM-dd");

  const form = useForm<ClosePositionFormValues>({
    resolver: zodResolver(closePositionSchema),
    defaultValues: {
      id: position.id,
      close_date: formattedToday,
      closing_price: 0,
    },
  });

  async function onSubmit(data: ClosePositionFormValues) {
    setIsSubmitting(true);

    try {
      const result = await closePosition(data);

      if (result.success) {
        setIsOpen(false);
        onClose();
      } else {
        form.setError("root", {
          message: result.error || "Failed to close position",
        });
      }
    } catch {
      form.setError("root", {
        message: "An unexpected error occurred",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger ? (
          trigger
        ) : (
          <Button variant="secondary">
            <X className="mr-2 h-4 w-4" />
            Close Position
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Close Position: {position.symbol}</DialogTitle>
          <DialogDescription>
            Enter the closing price to calculate profit/loss and close this
            position.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="close_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Close Date</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      placeholder="Select close date"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="closing_price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Closing Price</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="Enter closing price"
                      {...field}
                      value={field.value === 0 ? "" : field.value}
                      onChange={(e) => {
                        const value = e.target.value;
                        // Always ensure we store a number, never NaN or string
                        const numericValue =
                          value === "" ? 0 : parseFloat(value);
                        field.onChange(isNaN(numericValue) ? 0 : numericValue);
                      }}
                      onFocus={(e) => {
                        // Select all text if the value is 0 (default)
                        if (field.value === 0) {
                          e.target.select();
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                  <p className="text-xs text-muted-foreground">
                    {position.credit_debit_amount >= 0
                      ? "For credit strategies, a lower closing price means more profit"
                      : "For debit strategies, a higher closing price means more profit"}
                  </p>
                </FormItem>
              )}
            />

            {form.formState.errors.root && (
              <div className="text-sm font-medium text-red-500">
                {form.formState.errors.root.message}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Closing..." : "Close Position"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
