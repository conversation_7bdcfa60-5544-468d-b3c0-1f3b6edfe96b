"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { deletePosition } from "@/lib/actions/positions";
import { Position } from "@/types/position";
import { Trash2 } from "lucide-react";

interface DeletePositionDialogProps {
  position: Position;
  onDelete: () => void;
}

export function DeletePositionDialog({
  position,
  onDelete,
}: DeletePositionDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  async function handleDelete() {
    setIsDeleting(true);
    setError(null);

    try {
      const result = await deletePosition(position.id);

      if (result.success) {
        setIsOpen(false);
        onDelete();
      } else {
        setError(result.error || "Failed to delete position");
      }
    } catch {
      setError("An unexpected error occurred");
    } finally {
      setIsDeleting(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="destructive">
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Position
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Position: {position.symbol}</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this position? This action cannot be
            undone.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <div className="text-sm font-medium text-red-500 mb-4">{error}</div>
        )}

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete Position"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
