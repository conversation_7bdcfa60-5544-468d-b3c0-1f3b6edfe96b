"use client";

import { useEffect, useState } from "react";
import { parseISO } from "date-fns";

interface DTEDisplayClientProps {
  expiryDate: string;
  status?: string;
  closeDate?: string | null;
}

/**
 * Calculate Days to Expiry (DTE) from an expiry date
 */
function calculateDTE(expiryDate: string): number {
  const today = new Date();
  const expiry = parseISO(expiryDate);

  // Set both dates to midnight UTC for accurate day calculation
  const todayUTC = Date.UTC(
    today.getUTCFullYear(),
    today.getUTCMonth(),
    today.getUTCDate()
  );
  const expiryUTC = Date.UTC(
    expiry.getUTCFullYear(),
    expiry.getUTCMonth(),
    expiry.getUTCDate()
  );

  // Calculate difference in days
  const millisecondsPerDay = 1000 * 60 * 60 * 24;
  const differenceInDays = Math.round(
    (expiryUTC - todayUTC) / millisecondsPerDay
  );

  return differenceInDays;
}

/**
 * Client-only component to display Days to Expiry (DTE) with appropriate color coding
 * This avoids hydration errors by only rendering on the client
 */
export function DTEDisplayClient({
  expiryDate,
  status = "open",
  closeDate = null,
}: DTEDisplayClientProps) {
  const [dteInfo, setDteInfo] = useState<{
    text: string;
    colorClass: string;
  } | null>(null);

  useEffect(() => {
    // Calculate DTE regardless of status
    const dte = calculateDTE(expiryDate);

    // If position is closed, determine if it expired naturally or was manually closed
    if (status === "closed") {
      // If there's no close_date set by the user and DTE is negative,
      // it was likely auto-closed due to expiry
      if (!closeDate && dte < 0) {
        setDteInfo({
          text: "Expired",
          colorClass: "text-white/70",
        });
      } else {
        setDteInfo({
          text: "Position closed",
          colorClass: "text-white/70",
        });
      }
      return;
    }

    // For open positions, continue with the existing logic

    // Determine text content
    let text: string;
    if (dte < 0) {
      text = "Expired";
    } else {
      text = `${dte} DTE`;
    }

    // Determine text color class
    let colorClass: string;
    if (dte < 0) {
      colorClass = "text-red-400 font-medium"; // Expired - lighter red for dark background
    } else if (dte < 7) {
      colorClass = "text-red-400"; // Less than 7 days - lighter red for dark background
    } else if (dte < 21) {
      colorClass = "text-orange-400"; // Less than 21 days - lighter orange for dark background
    } else {
      colorClass = "text-white"; // Default color for dark background
    }

    setDteInfo({ text, colorClass });

    // Optional: Set up an interval to refresh DTE at midnight
    const intervalId = setInterval(() => {
      const newDte = calculateDTE(expiryDate);
      if (newDte !== dte) {
        setDteInfo({ text, colorClass });
      }
    }, 3600000); // Check every hour

    return () => clearInterval(intervalId);
  }, [expiryDate, status, closeDate]);

  // Return empty span during SSR to avoid hydration mismatch
  if (!dteInfo) {
    return <span className="font-medium"></span>;
  }

  return (
    <span
      className={`font-medium ${dteInfo.colorClass} cursor-help`}
      title={dteInfo.text}
    >
      {dteInfo.text}
    </span>
  );
}
