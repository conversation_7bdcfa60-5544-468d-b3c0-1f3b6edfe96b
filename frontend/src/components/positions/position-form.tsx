"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { SymbolSearch } from "@/components/stock/symbol-search";
import { usePriceContext } from "@/lib/contexts/price-context";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TagInput } from "@/components/positions/tag-input";
import { createPosition, updatePosition } from "@/lib/actions/positions";
import { Position, NewPosition, UpdatePosition } from "@/types/position";

const strategyOptions = [
  "Call Option",
  "Put Option",
  "Call Spread",
  "Put Spread",
  "Iron Condor",
  "Butterfly",
  "Straddle",
  "Strangle",
  "Covered Call",
  "Cash Secured Put",
  "Other",
];

// Define a custom validation schema for the form
const positionFormSchema = z.object({
  symbol: z
    .string()
    .min(1, "Symbol is required")
    .max(10, "Symbol must be 10 characters or less"),
  strategy_type: z.string().min(1, "Strategy type is required"),
  open_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  expiry_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  credit_debit_amount: z.number(),
  stock_price_at_open: z.number().nullable(),
  vix_value: z.number().nullable(),
  num_contracts: z.number().int().positive().default(1),
  commission_paid: z.number().nullable(),
  notes: z.string().nullable(),
  close_date: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format")
    .nullable(),
});

type PositionFormValues = z.infer<typeof positionFormSchema>;

interface PositionFormProps {
  position?: Position;
  isEditing?: boolean;
}

export function PositionForm({
  position,
  isEditing = false,
}: PositionFormProps) {
  const router = useRouter();
  const { clearInvalidSymbols } = usePriceContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [strikeInputs, setStrikeInputs] = useState<string[]>(["0"]);
  const [formError, setFormError] = useState<string | null>(null);
  const [selectedSymbol, setSelectedSymbol] = useState<string>(
    position?.symbol || ""
  );

  // Clear invalid symbols when the form mounts
  useEffect(() => {
    clearInvalidSymbols();
  }, [clearInvalidSymbols]);

  // Set up the form with default values
  const form = useForm<PositionFormValues>({
    resolver: zodResolver(positionFormSchema),
    defaultValues: {
      symbol: position?.symbol || "",
      strategy_type: position?.strategy_type || "",
      open_date: position?.open_date || new Date().toISOString().slice(0, 10),
      expiry_date: position?.expiry_date || "",
      credit_debit_amount: position?.credit_debit_amount || 0,
      stock_price_at_open: position?.stock_price_at_open || null,
      vix_value: position?.vix_value || null,
      num_contracts: position?.num_contracts || 1,
      commission_paid: position?.commission_paid || null,
      notes: position?.notes || null,
      close_date: position?.close_date || null,
    },
  });

  // Update the form when a symbol is selected from the search
  const handleSymbolSelect = (
    symbol: string,
    name?: string,
    isClearing?: boolean
  ) => {
    setSelectedSymbol(symbol);
    form.setValue("symbol", symbol);

    // Only trigger validation if this is not a clearing action
    if (!isClearing) {
      form.trigger("symbol");
    }
  };

  // Initialize strike prices and tags from existing position
  useEffect(() => {
    if (position) {
      // Set strike price inputs
      if (position.strike_prices && position.strike_prices.length > 0) {
        setStrikeInputs(
          position.strike_prices.map((price) => price.toString())
        );
      }

      // Set tags if position has them
      if (position.tags) {
        setTags(
          position.tags.map((tag) =>
            typeof tag === "string" ? tag : tag.tag_name
          )
        );
      }
    }
  }, [position]);

  // Handle adding new strike price input
  const addStrikeInput = () => {
    setStrikeInputs([...strikeInputs, "0"]);
  };

  // Handle removing a strike price input
  const removeStrikeInput = (index: number) => {
    const newInputs = [...strikeInputs];
    newInputs.splice(index, 1);
    setStrikeInputs(newInputs);
  };

  // Handle changing a strike price value
  const handleStrikeChange = (index: number, value: string) => {
    const newInputs = [...strikeInputs];
    newInputs[index] = value;
    setStrikeInputs(newInputs);
  };

  // Handle form submission
  const onSubmit = async (data: PositionFormValues) => {
    setIsSubmitting(true);
    setFormError(null);

    try {
      // Convert strike price inputs to numbers
      const strike_prices = strikeInputs
        .filter((input) => input.trim() !== "")
        .map((input) => parseFloat(input));

      if (strike_prices.length === 0) {
        setFormError("At least one strike price is required");
        setIsSubmitting(false);
        return;
      }

      let result;

      if (isEditing && position) {
        // Update existing position
        const updateData: UpdatePosition = {
          id: position.id,
          ...data,
          strike_prices,
          tags,
        };

        result = await updatePosition(updateData);
      } else {
        // Create new position
        const createData: NewPosition = {
          ...data,
          strike_prices,
          tags,
          status: "open",
          profit_loss: null,
          closing_price: null,
        };

        result = await createPosition(createData);
      }

      if (!result || (result && !result.success)) {
        // Show error
        console.error(
          "Error saving position:",
          result?.error || "Unknown error"
        );
        setFormError(
          typeof result?.error === "string"
            ? result.error
            : "Failed to save position"
        );
      } else {
        router.push("/dashboard/positions");
        router.refresh();
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setFormError("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Symbol */}
          <FormField
            control={form.control}
            name="symbol"
            render={({ field }) => (
              <FormItem className="space-y-4">
                <FormLabel>Symbol</FormLabel>
                <FormControl>
                  <div className="space-y-2">
                    <SymbolSearch
                      onSelect={handleSymbolSelect}
                      placeholder="Search for a symbol or company name"
                      value={selectedSymbol}
                      clearOnSelect={false}
                    />
                  </div>
                </FormControl>

                <FormDescription>
                  Enter the stock or ETF symbol or search above.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Strategy Type */}
          <FormField
            control={form.control}
            name="strategy_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Strategy Type</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a strategy" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {strategyOptions.map((strategy) => (
                      <SelectItem key={strategy} value={strategy}>
                        {strategy}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>Select the options strategy.</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Open Date */}
          <FormField
            control={form.control}
            name="open_date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Open Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Expiry Date */}
          <FormField
            control={form.control}
            name="expiry_date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Expiry Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Credit/Debit Amount */}
          <FormField
            control={form.control}
            name="credit_debit_amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Credit/Debit Amount</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    {...field}
                    onChange={(e) =>
                      field.onChange(parseFloat(e.target.value) || 0)
                    }
                    onFocus={(e) => {
                      // Select all text if the value is 0 (default)
                      if (field.value === 0) {
                        e.target.select();
                      }
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Enter a positive value for credit, negative for debit.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Number of Contracts */}
          <FormField
            control={form.control}
            name="num_contracts"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Number of Contracts</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    placeholder="1"
                    {...field}
                    onChange={(e) =>
                      field.onChange(parseInt(e.target.value, 10) || 1)
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Stock Price at Open */}
          <FormField
            control={form.control}
            name="stock_price_at_open"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Stock Price at Open</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    {...field}
                    value={field.value || ""}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value ? parseFloat(e.target.value) : null
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* VIX Value */}
          <FormField
            control={form.control}
            name="vix_value"
            render={({ field }) => (
              <FormItem>
                <FormLabel>VIX Value</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="Optional"
                    {...field}
                    value={field.value || ""}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value ? parseFloat(e.target.value) : null
                      )
                    }
                  />
                </FormControl>
                <FormDescription>
                  VIX value at time of opening the position.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Commission Paid */}
          <FormField
            control={form.control}
            name="commission_paid"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Commission Paid</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    {...field}
                    value={field.value || ""}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value ? parseFloat(e.target.value) : null
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Strike Prices */}
        <div>
          <FormLabel>Strike Prices</FormLabel>
          <div className="space-y-2">
            {strikeInputs.map((input, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  type="number"
                  step="0.01"
                  placeholder={`Strike price ${index + 1}`}
                  value={input}
                  onChange={(e) => handleStrikeChange(index, e.target.value)}
                  onFocus={(e) => {
                    // Select all text if the value is "0" (default)
                    if (input === "0") {
                      e.target.select();
                    }
                  }}
                  className="flex-1"
                />
                {index > 0 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeStrikeInput(index)}
                  >
                    Remove
                  </Button>
                )}
              </div>
            ))}
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addStrikeInput}
            className="mt-2"
          >
            Add Strike Price
          </Button>
          {formError && formError.includes("strike price") && (
            <p className="text-sm font-medium text-destructive mt-2">
              {formError}
            </p>
          )}
        </div>

        {/* Tags */}
        <div>
          <FormLabel>Tags</FormLabel>
          <FormDescription className="mb-2">
            Add tags to categorize your position (press Enter or comma to add).
          </FormDescription>
          <TagInput tags={tags} onChange={setTags} placeholder="Add tags..." />
        </div>

        {/* Notes */}
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Add any additional notes or trade journal entries..."
                  className="min-h-[120px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form level error message */}
        {formError && !formError.includes("strike price") && (
          <div className="text-sm font-medium text-destructive">
            {formError}
          </div>
        )}

        {/* Form buttons */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/dashboard/positions")}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? "Saving..."
              : isEditing
              ? "Update Position"
              : "Create Position"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
