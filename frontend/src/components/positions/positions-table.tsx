"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  useReactTable,
  type SortingState,
  type PaginationState,
  type ColumnDef,
  type Row,
  type Table as TableType,
} from "@tanstack/react-table";
import { formatDate } from "@/lib/utils/date-formatter";
import { RelativeTime } from "@/components/ui/relative-time";
import { DTEDisplayClient } from "@/components/positions/dte-display-client";

import { type Position } from "@/types/position";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Pencil, X, Trash2 } from "lucide-react";
import { deletePosition } from "@/lib/actions/positions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CentralizedStockPrice } from "@/components/stock/centralized-stock-price";
import { ClosePositionDialog } from "@/components/positions/close-position-dialog";
import { usePriceContext } from "@/lib/contexts/price-context";

interface PositionsTableProps {
  positions: Position[];
}

export function PositionsTable({ positions }: PositionsTableProps) {
  const router = useRouter();
  const { forceRefreshClosedPositions } = usePriceContext();
  const [sorting, setSorting] = useState<SortingState>([
    { id: "open_date", desc: true },
  ]);

  // Filter states
  const [statusFilter, setStatusFilter] = useState<string>("open");
  const [tagFilter, setTagFilter] = useState<string | null>(null);

  // Track last tab switch for force refresh detection
  const lastTabSwitchRef = useRef<string>("open");

  // Manually manage pagination state
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // State to track when a position is modified so we can refresh
  const [, setRefreshKey] = useState(0);

  const handlePositionUpdated = () => {
    // Increment the refresh key to trigger a refresh
    setRefreshKey((prev) => prev + 1);

    // Refresh the UI
    router.refresh();
  };

  // Define columns
  const columns: ColumnDef<Position>[] = [
    {
      accessorKey: "symbol",
      header: "Symbol",
      enableSorting: true,
      cell: ({ row }: { row: Row<Position> }) => (
        <div className="flex flex-col">
          <Link
            href={`/dashboard/positions/${row.original.id}`}
            className="font-medium text-[#9d4edd] hover:underline"
          >
            {row.original.symbol.toUpperCase()}
          </Link>
          {/* Display only the symbol name */}
          <CentralizedStockPrice
            symbol={row.original.symbol}
            showName={true}
            className="mt-1"
            positionStatus={row.original.status}
          />
        </div>
      ),
    },
    {
      accessorKey: "strategy_type",
      header: "Strategy",
      enableSorting: true,
    },
    {
      accessorKey: "stock_price_at_open",
      header: "Price at Open",
      enableSorting: false,
      cell: ({ row }: { row: Row<Position> }) => {
        const price = row.original.stock_price_at_open;
        return (
          <span className="font-medium">
            {price !== null ? `$${price.toFixed(2)}` : "N/A"}
          </span>
        );
      },
    },
    {
      id: "currentPrice",
      header: "Stock Price Now",
      cell: ({ row }: { row: Row<Position> }) => (
        <CentralizedStockPrice
          symbol={row.original.symbol}
          showChange={true}
          showRefreshButton={true}
          positionStatus={row.original.status}
        />
      ),
    },
    {
      accessorKey: "open_date",
      header: "Opened",
      enableSorting: true,
      cell: ({ row }: { row: Row<Position> }) => (
        <div className="flex flex-col">
          <span>{formatDate(row.original.open_date)}</span>
          <RelativeTime date={row.original.open_date} />
        </div>
      ),
    },
    {
      accessorKey: "expiry_date",
      header: "Expiry",
      enableSorting: true,
      cell: ({ row }: { row: Row<Position> }) => (
        <div className="flex flex-col">
          <DTEDisplayClient
            expiryDate={row.original.expiry_date}
            status={row.original.status}
            closeDate={row.original.close_date}
          />
          <span className="text-xs text-white/70">
            {formatDate(row.original.expiry_date)}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "strike_prices",
      header: "Strike Prices",
      cell: ({ row }: { row: Row<Position> }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.strike_prices.map((price: number, i: number) => (
            <span
              key={i}
              className="text-xs font-mono bg-[#2a0052] text-white p-1 rounded"
            >
              {price}
            </span>
          ))}
        </div>
      ),
    },
    {
      accessorKey: "credit_debit_amount",
      header: "Amount",
      enableSorting: true,
      cell: ({ row }: { row: Row<Position> }) => (
        <span
          className={
            row.original.credit_debit_amount >= 0
              ? "text-green-600"
              : "text-red-600"
          }
        >
          ${Math.abs(row.original.credit_debit_amount).toFixed(2)}
          <span className="text-xs ml-1">
            {row.original.credit_debit_amount >= 0 ? "CR" : "DB"}
          </span>
        </span>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      enableSorting: true,
      cell: ({ row }: { row: Row<Position> }) => (
        <Badge
          variant={row.original.status === "open" ? "outline" : "secondary"}
          className={
            row.original.status === "open"
              ? "border-green-500 text-green-400"
              : "border-gray-500 text-gray-400"
          }
        >
          {row.original.status.toUpperCase()}
        </Badge>
      ),
    },
    {
      accessorKey: "profit_loss",
      header: "P/L",
      enableSorting: true,
      cell: ({ row }: { row: Row<Position> }) => {
        const pl = row.original.profit_loss;
        if (pl === null) return <span className="text-white/70">-</span>;

        return (
          <span
            className={
              pl >= 0
                ? "text-green-600 font-medium"
                : "text-red-600 font-medium"
            }
          >
            {pl >= 0 ? "+" : ""}${pl.toFixed(2)}
          </span>
        );
      },
    },
    {
      accessorKey: "tags",
      header: "Tags",
      cell: ({ row }: { row: Row<Position> }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.tags && row.original.tags.length > 0 ? (
            row.original.tags.map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="text-xs text-white bg-[#3d2a50] hover:bg-[#4d3a60]"
              >
                {typeof tag === "string" ? tag : tag.tag_name}
              </Badge>
            ))
          ) : (
            <span className="text-white/70 text-xs">No tags</span>
          )}
        </div>
      ),
    },
    {
      id: "actions",
      cell: ({ row }: { row: Row<Position> }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link href={`/dashboard/positions/${row.original.id}`}>
                View details
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/dashboard/positions/${row.original.id}/edit`}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit position
              </Link>
            </DropdownMenuItem>
            {row.original.status === "open" && (
              <DropdownMenuItem onSelect={(e) => e.preventDefault()} asChild>
                <ClosePositionDialog
                  position={row.original}
                  onClose={handlePositionUpdated}
                  trigger={
                    <div className="flex items-center cursor-pointer w-full">
                      <X className="mr-2 h-4 w-4" />
                      Close position
                    </div>
                  }
                />
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onSelect={(e) => e.preventDefault()}
              className="text-red-600"
            >
              <div
                onClick={async () => {
                  // Simple inline delete
                  if (
                    window.confirm(
                      `Are you sure you want to delete ${row.original.symbol}?`
                    )
                  ) {
                    const result = await deletePosition(row.original.id);
                    if (result.success) {
                      handlePositionUpdated();
                    }
                  }
                }}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete position
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  // Use memoization to prevent unnecessary recalculations
  const openCount = useMemo(
    () => positions.filter((p) => p.status === "open").length,
    [positions]
  );
  const closedCount = useMemo(
    () => positions.filter((p) => p.status === "closed").length,
    [positions]
  );

  // Get all unique tags from positions for the filter dropdown
  const allTags = useMemo(() => {
    const tags = new Set<string>();
    positions.forEach((position) => {
      if (position.tags && position.tags.length > 0) {
        position.tags.forEach((tag) => {
          const tagName = typeof tag === "string" ? tag : tag.tag_name;
          tags.add(tagName);
        });
      }
    });
    return Array.from(tags).sort();
  }, [positions]);

  // First filter by status
  const statusFilteredPositions = useMemo(
    () =>
      positions.filter((position) =>
        statusFilter === "all" ? true : position.status === statusFilter
      ),
    [positions, statusFilter]
  );

  // Then filter by tag if a tag filter is selected
  const filteredPositions = useMemo(() => {
    if (!tagFilter) return statusFilteredPositions;

    return statusFilteredPositions.filter((position) => {
      if (!position.tags || position.tags.length === 0) return false;

      return position.tags.some((tag) => {
        const tagName = typeof tag === "string" ? tag : tag.tag_name;
        return tagName === tagFilter;
      });
    });
  }, [statusFilteredPositions, tagFilter]);

  // Create a ref to store the table instance
  const tableInstanceRef = useRef<TableType<Position> | null>(null);

  // Reset pagination when filter changes - before table is created
  useEffect(() => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [statusFilter, tagFilter]);

  // Force refresh closed positions when user switches to closed tab
  useEffect(() => {
    const previousTab = lastTabSwitchRef.current;

    // If user just switched to "closed" tab from another tab
    if (statusFilter === "closed" && previousTab !== "closed") {
      console.log("User switched to closed positions tab - forcing refresh");
      forceRefreshClosedPositions();
    }

    // Update the ref to track current tab
    lastTabSwitchRef.current = statusFilter;
  }, [statusFilter, forceRefreshClosedPositions]);

  // Create table with memoized data
  const table = useReactTable({
    data: filteredPositions,
    columns,
    state: {
      sorting,
      pagination,
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  // Store the table instance in the ref after it's created
  useEffect(() => {
    tableInstanceRef.current = table;
  }, [table]);

  return (
    <div className="w-full">
      {/* Filter buttons */}
      <div className="flex flex-wrap justify-between items-center mb-4">
        <div className="flex space-x-2 mb-2">
          <Button
            variant={statusFilter === "open" ? "default" : "outline"}
            onClick={() => setStatusFilter("open")}
            className="text-sm"
            size="sm"
          >
            Open
            <Badge variant="secondary" className="ml-2 bg-[#3d2a50]">
              {openCount}
            </Badge>
          </Button>
          <Button
            variant={statusFilter === "closed" ? "default" : "outline"}
            onClick={() => setStatusFilter("closed")}
            className="text-sm"
            size="sm"
          >
            Closed
            <Badge variant="secondary" className="ml-2 bg-[#3d2a50]">
              {closedCount}
            </Badge>
          </Button>
          <Button
            variant={statusFilter === "all" ? "default" : "outline"}
            onClick={() => setStatusFilter("all")}
            className="text-sm"
            size="sm"
          >
            All
            <Badge variant="secondary" className="ml-2 bg-[#3d2a50]">
              {positions.length}
            </Badge>
          </Button>
        </div>

        {/* Tag filter */}
        {allTags.length > 0 && (
          <div className="flex items-center space-x-2 mb-2">
            <Select
              value={tagFilter || "all"}
              onValueChange={(value) => {
                setTagFilter(value === "all" ? null : value);
                setPagination((prev) => ({ ...prev, pageIndex: 0 })); // Reset to first page on filter change
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by tag" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tags</SelectItem>
                {allTags.map((tag) => (
                  <SelectItem key={tag} value={tag}>
                    {tag}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {tagFilter && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTagFilter(null)}
                className="h-8 px-2"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Clear tag filter</span>
              </Button>
            )}
          </div>
        )}
      </div>

      <div className="rounded-md border">
        <Table key={`${statusFilter}-${tagFilter || "all"}`}>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder ? null : (
                      <div
                        className={
                          header.column.getCanSort()
                            ? "cursor-pointer select-none flex items-center"
                            : ""
                        }
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {header.column.getCanSort() && (
                          <span className="ml-1">
                            {{
                              asc: "↑",
                              desc: "↓",
                            }[header.column.getIsSorted() as string] ?? "⇅"}
                          </span>
                        )}
                      </div>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {statusFilter === "all"
                    ? "No positions found."
                    : `No ${statusFilter} positions found.`}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            // Use setPagination directly instead of table methods
            setPagination((prev) => ({
              ...prev,
              pageIndex: Math.max(0, prev.pageIndex - 1),
            }));
          }}
          disabled={!table.getCanPreviousPage()}
        >
          Previous
        </Button>
        <span className="mx-2">
          Page {pagination.pageIndex + 1} of {Math.max(1, table.getPageCount())}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            // Use setPagination directly instead of table methods
            setPagination((prev) => ({
              ...prev,
              pageIndex: Math.min(table.getPageCount() - 1, prev.pageIndex + 1),
            }));
          }}
          disabled={!table.getCanNextPage()}
        >
          Next
        </Button>
      </div>
    </div>
  );
}
