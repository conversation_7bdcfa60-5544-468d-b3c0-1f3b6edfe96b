-- Create users table (will work alongside <PERSON><PERSON><PERSON> Auth)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT auth.uid(),
  email TEXT NOT NULL UNIQUE,
  profile_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security for users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policy for users to only see and edit their own profiles
CREATE POLICY "Users can only view their own profile"
  ON users
  FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can only update their own profile"
  ON users
  FOR UPDATE
  USING (auth.uid() = id);

-- Create positions table for tracking options trades
CREATE TABLE IF NOT EXISTS positions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  symbol TEXT NOT NULL,
  strategy_type TEXT NOT NULL,
  open_date DATE NOT NULL,
  close_date DATE,
  expiry_date DATE NOT NULL,
  strike_prices NUMERIC[] NOT NULL,
  credit_debit_amount NUMERIC NOT NULL,
  stock_price_at_open NUMERIC,
  vix_value NUMERIC,
  num_contracts INTEGER DEFAULT 1,
  commission_paid NUMERIC,
  notes TEXT,
  status TEXT NOT NULL DEFAULT 'open',
  profit_loss NUMERIC,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_status CHECK (status IN ('open', 'closed'))
);

-- Enable Row Level Security for positions table
ALTER TABLE positions ENABLE ROW LEVEL SECURITY;

-- Create policy for users to only see and manage their own positions
CREATE POLICY "Users can only view their own positions"
  ON positions
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can only insert their own positions"
  ON positions
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can only update their own positions"
  ON positions
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can only delete their own positions"
  ON positions
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX positions_user_id_idx ON positions(user_id);
CREATE INDEX positions_symbol_idx ON positions(symbol);
CREATE INDEX positions_status_idx ON positions(status);
CREATE INDEX positions_open_date_idx ON positions(open_date);
CREATE INDEX positions_expiry_date_idx ON positions(expiry_date); 