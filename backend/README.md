# Five Rivers Options - Backend

This directory contains server-side logic, database setup scripts, and API endpoints for the Five Rivers Options application.

## Supabase Setup

The application uses Supabase for authentication, database, and storage. The following scripts are provided to set up the necessary database components:

### SQL Scripts

All SQL scripts are located in the `scripts/supabase` directory:

1. `01_profiles_setup.sql`: Creates the profiles table with proper relationships to Supabase auth.users, sets up Row Level Security policies, and implements triggers for automatic profile creation.

2. `02_storage_setup.sql`: Configures storage buckets for user avatars with appropriate security policies and sets up triggers to update user profiles when new avatars are uploaded.

### Running the Setup Scripts

To run the setup scripts against your Supabase PostgreSQL database:

1. Ensure you have the PostgreSQL client tools installed.

2. Update the database connection details in `frontend/.env.psql`:

   ```
   PGHOST=your-supabase-db-host
   PGUSER=postgres
   PGPASSWORD=your-postgres-password
   PGDATABASE=postgres
   PGPORT=5432
   ```

3. Run the setup script:

   ```bash
   cd backend/scripts
   ./run_scripts.sh
   ```

## Database Schema

### Profiles Table

| Column     | Type                     | Description                        |
| ---------- | ------------------------ | ---------------------------------- |
| id         | UUID (Primary Key)       | References auth.users(id)          |
| full_name  | TEXT                     | User's full name                   |
| avatar_url | TEXT                     | URL to user's avatar image         |
| created_at | TIMESTAMP WITH TIME ZONE | Timestamp when profile was created |
| updated_at | TIMESTAMP WITH TIME ZONE | Timestamp when profile was updated |

### Storage Buckets

- **avatars**: Bucket for storing user avatar images
  - File size limit: 5MB
  - Allowed mime types: image/png, image/jpeg, image/gif, image/webp, image/svg+xml
  - Security: Private bucket with RLS policies for user-specific access

## Security

- Row Level Security (RLS) is enabled on both the profiles table and storage buckets
- Users can only read and update their own profiles
- Users can only upload, view, update, and delete their own avatars
- Triggers automatically create user profiles upon signup and update avatar URLs when uploaded
