-- Create position_tags table for tagging functionality
CREATE TABLE IF NOT EXISTS public.position_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL REFERENCES public.positions(id) ON DELETE CASCADE,
    tag_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(position_id, tag_name)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS position_tags_position_id_idx ON public.position_tags(position_id);
CREATE INDEX IF NOT EXISTS position_tags_tag_name_idx ON public.position_tags(tag_name);

-- Enable Row Level Security
ALTER TABLE public.position_tags ENABLE ROW LEVEL SECURITY;

-- Create policy for selecting tags
CREATE POLICY "Users can view tags for their own positions" 
ON public.position_tags 
FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.positions 
        WHERE positions.id = position_tags.position_id 
        AND positions.user_id = auth.uid()
    )
);

-- Create policy for inserting tags
CREATE POLICY "Users can insert tags for their own positions" 
ON public.position_tags 
FOR INSERT 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.positions 
        WHERE positions.id = position_tags.position_id 
        AND positions.user_id = auth.uid()
    )
);

-- Create policy for updating tags
CREATE POLICY "Users can update tags for their own positions" 
ON public.position_tags 
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM public.positions 
        WHERE positions.id = position_tags.position_id 
        AND positions.user_id = auth.uid()
    )
);

-- Create policy for deleting tags
CREATE POLICY "Users can delete tags for their own positions" 
ON public.position_tags 
FOR DELETE 
USING (
    EXISTS (
        SELECT 1 FROM public.positions 
        WHERE positions.id = position_tags.position_id 
        AND positions.user_id = auth.uid()
    )
);

-- Grant privileges
GRANT ALL ON public.position_tags TO postgres;
GRANT ALL ON public.position_tags TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.position_tags TO authenticated;

-- Add comments
COMMENT ON TABLE public.position_tags IS 'Stores tags for positions to enable position categorization';
COMMENT ON COLUMN public.position_tags.id IS 'Primary key for the tag';
COMMENT ON COLUMN public.position_tags.position_id IS 'Reference to the positions table';
COMMENT ON COLUMN public.position_tags.tag_name IS 'Name of the tag';
COMMENT ON COLUMN public.position_tags.created_at IS 'Timestamp when the tag was created'; 