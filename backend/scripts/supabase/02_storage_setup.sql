-- Create storage bucket for avatars if it doesn't exist
INSERT INTO storage.buckets (id, name, public, avif_autodetection, file_size_limit, allowed_mime_types, owner, created_at, updated_at)
VALUES (
  'avatars',
  'avatars',
  false, -- not public, will be controlled through policies
  false, -- no avif autodetection
  5242880, -- 5MB file size limit
  ARRAY['image/png', 'image/jpeg', 'image/gif', 'image/webp', 'image/svg+xml']::text[], -- allowed mime types
  NULL, -- owner
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- Enable Row Level Security on the objects table for the avatars bucket
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Allow users to upload their own avatar
CREATE POLICY "Users can upload their own avatar"
  ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = auth.uid()::text
  );

-- Allow users to update their own avatar
CREATE POLICY "Users can update their own avatar"
  ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = auth.uid()::text
  );

-- Allow users to delete their own avatar
CREATE POLICY "Users can delete their own avatar"
  ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = auth.uid()::text
  );

-- Allow users to view their own avatar
CREATE POLICY "Users can view their own avatar"
  ON storage.objects
  FOR SELECT
  USING (
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = auth.uid()::text
  );

-- Allow authenticated users to view any avatar (needed for displaying avatars on comments, etc)
CREATE POLICY "Authenticated users can view any avatar"
  ON storage.objects
  FOR SELECT
  USING (
    bucket_id = 'avatars' AND
    auth.role() = 'authenticated'
  );

-- Function to get a presigned URL for an avatar
CREATE OR REPLACE FUNCTION public.get_avatar_url(user_id UUID)
RETURNS TEXT AS $$
DECLARE
  avatar_url TEXT;
BEGIN
  -- Get the avatar URL for the user
  SELECT storage.url(name) INTO avatar_url
  FROM storage.objects
  WHERE bucket_id = 'avatars' AND (storage.foldername(name))[1] = user_id::text
  ORDER BY created_at DESC
  LIMIT 1;

  RETURN avatar_url;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to update a user's avatar URL in the profiles table
CREATE OR REPLACE FUNCTION public.update_avatar_url()
RETURNS TRIGGER AS $$
DECLARE
  user_id UUID;
BEGIN
  -- Extract user ID from path
  user_id := (storage.foldername(NEW.name))[1]::UUID;
  
  -- Update profile with new avatar URL
  UPDATE public.profiles
  SET avatar_url = storage.url(NEW.name), updated_at = NOW()
  WHERE id = user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to update the avatar URL in profiles when a new avatar is uploaded
DROP TRIGGER IF EXISTS on_avatar_upload ON storage.objects;
CREATE TRIGGER on_avatar_upload
AFTER INSERT OR UPDATE ON storage.objects
FOR EACH ROW
WHEN (NEW.bucket_id = 'avatars')
EXECUTE FUNCTION public.update_avatar_url();

-- Comment on functions for documentation
COMMENT ON FUNCTION public.get_avatar_url IS 'Returns the URL for a user''s avatar';
COMMENT ON FUNCTION public.update_avatar_url IS 'Updates the avatar_url in profiles when a new avatar is uploaded'; 