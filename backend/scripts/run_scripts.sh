#!/bin/bash

# Exit on error
set -e

# Load environment variables from the .env.psql file
if [ -f ../../frontend/.env.psql ]; then
  source ../../frontend/.env.psql
else
  echo "Error: .env.psql file not found"
  exit 1
fi

# Validate required environment variables
if [ -z "$PGHOST" ] || [ -z "$PGUSER" ] || [ -z "$PGPASSWORD" ] || [ -z "$PGDATABASE" ] || [ -z "$PGPORT" ]; then
  echo "Error: Missing required database connection variables in .env.psql"
  exit 1
fi

echo "Connecting to Supabase PostgreSQL database..."
echo "Host: $PGHOST"
echo "Database: $PGDATABASE"
echo "Port: $PGPORT"
echo "User: $PGUSER"
echo "Running scripts..."

# Run the SQL scripts in order
for script in supabase/*.sql; do
  if [ -f "$script" ]; then
    echo "Running script: $script"
    PGPASSWORD=$PGPASSWORD psql -h $PGHOST -U $PGUSER -d $PGDATABASE -p $PGPORT -f "$script"
    echo "Completed: $script"
  fi
done

echo "All scripts executed successfully." 